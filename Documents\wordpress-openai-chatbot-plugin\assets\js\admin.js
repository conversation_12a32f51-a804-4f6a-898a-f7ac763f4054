/**
 * OpenAI Chatbot Admin JavaScript
 * 
 * Handles admin interface interactions, form validation, and AJAX requests
 * Provides enhanced user experience for the WordPress admin settings page
 * 
 * @package OpenAI_Chatbot
 */

(function($) {
    'use strict';

    /**
     * Admin interface handler
     */
    class OpenAIChatbotAdmin {
        
        /**
         * Constructor
         */
        constructor() {
            this.init();
        }

        /**
         * Initialize admin functionality
         */
        init() {
            this.bindEvents();
            this.initFormValidation();
            this.initTooltips();
            this.checkApiConfiguration();
            
            console.log('OpenAI Chatbot Admin initialized');
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            // Form submission with validation
            $('form').on('submit', this.handleFormSubmit.bind(this));
            
            // API test button
            $('input[name="test_api"]').on('click', this.handleApiTest.bind(this));
            
            // Real-time form validation
            $('input, textarea, select').on('change blur', this.validateField.bind(this));
            
            // Character count for textareas
            $('textarea[maxlength]').on('input', this.updateCharacterCount.bind(this));
            
            // Rate limit field dependencies
            $('input[name^="openai_chatbot_rate_limit"]').on('change', this.validateRateLimits.bind(this));
            
            // Theme preview
            $('select[name="openai_chatbot_theme"]').on('change', this.previewTheme.bind(this));
            
            // Position preview
            $('select[name="openai_chatbot_position"]').on('change', this.previewPosition.bind(this));
            
            // Custom CSS validation
            $('textarea[name="openai_chatbot_custom_css"]').on('blur', this.validateCustomCSS.bind(this));
            
            // Settings import/export (if implemented)
            $('.export-settings').on('click', this.exportSettings.bind(this));
            $('.import-settings').on('click', this.importSettings.bind(this));
            
            // Help toggles
            $('.help-toggle').on('click', this.toggleHelp.bind(this));
            
            // Reset to defaults
            $('.reset-defaults').on('click', this.resetToDefaults.bind(this));
        }

        /**
         * Initialize form validation
         */
        initFormValidation() {
            // Add validation classes to required fields
            $('input[required], textarea[required], select[required]').addClass('required-field');
            
            // Initialize character counters
            $('textarea[maxlength]').each((index, element) => {
                this.addCharacterCounter($(element));
            });
            
            // Initialize field dependencies
            this.initFieldDependencies();
        }

        /**
         * Add character counter to textarea
         */
        addCharacterCounter($textarea) {
            const maxLength = $textarea.attr('maxlength');
            const currentLength = $textarea.val().length;
            
            const counter = $(`
                <div class="character-counter">
                    <span class="current">${currentLength}</span>/<span class="max">${maxLength}</span>
                </div>
            `);
            
            $textarea.after(counter);
            
            // Update counter on input
            $textarea.on('input', () => {
                const current = $textarea.val().length;
                counter.find('.current').text(current);
                
                // Add warning class if approaching limit
                counter.toggleClass('warning', current > maxLength * 0.9);
                counter.toggleClass('error', current >= maxLength);
            });
        }

        /**
         * Initialize field dependencies
         */
        initFieldDependencies() {
            // Enable/disable fields based on main enable checkbox
            const $enableCheckbox = $('input[name="openai_chatbot_enabled"]');
            const $dependentFields = $('.dependent-field');
            
            const toggleDependentFields = () => {
                const isEnabled = $enableCheckbox.is(':checked');
                $dependentFields.prop('disabled', !isEnabled);
                $dependentFields.closest('tr').toggleClass('disabled', !isEnabled);
            };
            
            $enableCheckbox.on('change', toggleDependentFields);
            toggleDependentFields(); // Initial state
        }

        /**
         * Handle form submission
         */
        handleFormSubmit(e) {
            const $form = $(e.target);
            
            // Skip validation for API test
            if ($(e.originalEvent.submitter).attr('name') === 'test_api') {
                return true;
            }
            
            // Validate form
            if (!this.validateForm($form)) {
                e.preventDefault();
                this.showValidationErrors();
                return false;
            }
            
            // Show loading state
            this.showLoadingState($form);
            
            return true;
        }

        /**
         * Validate entire form
         */
        validateForm($form) {
            let isValid = true;
            const errors = [];
            
            // Validate required fields
            $form.find('.required-field').each((index, element) => {
                const $field = $(element);
                if (!$field.val().trim()) {
                    isValid = false;
                    errors.push(`${this.getFieldLabel($field)} is required`);
                    $field.addClass('error');
                }
            });
            
            // Validate rate limits
            if (!this.validateRateLimits()) {
                isValid = false;
                errors.push('Rate limit values are invalid');
            }
            
            // Validate message length
            const maxLength = parseInt($('input[name="openai_chatbot_max_message_length"]').val());
            if (maxLength < 100 || maxLength > 2000) {
                isValid = false;
                errors.push('Message length must be between 100 and 2000 characters');
            }
            
            // Store errors for display
            this.validationErrors = errors;
            
            return isValid;
        }

        /**
         * Validate individual field
         */
        validateField(e) {
            const $field = $(e.target);
            const fieldName = $field.attr('name');
            let isValid = true;
            
            // Remove existing error state
            $field.removeClass('error');
            $field.next('.field-error').remove();
            
            // Required field validation
            if ($field.hasClass('required-field') && !$field.val().trim()) {
                isValid = false;
                this.showFieldError($field, 'This field is required');
            }
            
            // Specific field validations
            switch (fieldName) {
                case 'openai_chatbot_max_message_length':
                    const length = parseInt($field.val());
                    if (length < 100 || length > 2000) {
                        isValid = false;
                        this.showFieldError($field, 'Must be between 100 and 2000');
                    }
                    break;
                    
                case 'openai_chatbot_bot_name':
                    if ($field.val().length > 50) {
                        isValid = false;
                        this.showFieldError($field, 'Bot name too long (max 50 characters)');
                    }
                    break;
                    
                case 'openai_chatbot_welcome_message':
                    if ($field.val().length > 500) {
                        isValid = false;
                        this.showFieldError($field, 'Welcome message too long (max 500 characters)');
                    }
                    break;
            }
            
            return isValid;
        }

        /**
         * Validate rate limit settings
         */
        validateRateLimits() {
            const anonymous = parseInt($('input[name="openai_chatbot_rate_limit_anonymous"]').val()) || 0;
            const user = parseInt($('input[name="openai_chatbot_rate_limit_user"]').val()) || 0;
            const global = parseInt($('input[name="openai_chatbot_rate_limit_global"]').val()) || 0;
            const burst = parseInt($('input[name="openai_chatbot_burst_limit"]').val()) || 0;
            
            // Basic validation
            if (anonymous < 1 || user < 1 || global < 1 || burst < 1) {
                return false;
            }
            
            // Logical validation
            if (user < anonymous) {
                this.showFieldError($('input[name="openai_chatbot_rate_limit_user"]'), 
                    'User limit should be higher than anonymous limit');
                return false;
            }
            
            if (global < Math.max(anonymous, user) * 10) {
                this.showFieldError($('input[name="openai_chatbot_rate_limit_global"]'), 
                    'Global limit seems too low for the per-user limits');
                return false;
            }
            
            return true;
        }

        /**
         * Update character count display
         */
        updateCharacterCount(e) {
            const $textarea = $(e.target);
            const current = $textarea.val().length;
            const $counter = $textarea.siblings('.character-counter');
            
            if ($counter.length) {
                $counter.find('.current').text(current);
                
                const max = parseInt($counter.find('.max').text());
                $counter.toggleClass('warning', current > max * 0.9);
                $counter.toggleClass('error', current >= max);
            }
        }

        /**
         * Handle API connection test
         */
        handleApiTest(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const originalText = $button.val();
            
            // Show loading state
            $button.val('Testing...').prop('disabled', true);
            
            // Add spinner
            $button.after('<span class="spinner is-active"></span>');
            
            // The form will handle the actual test via PHP
            // We just need to restore the button state after page reload
            setTimeout(() => {
                $button.val(originalText).prop('disabled', false);
                $('.spinner').remove();
            }, 1000);
        }

        /**
         * Preview theme changes
         */
        previewTheme(e) {
            const theme = $(e.target).val();
            
            // Create preview notice
            this.showPreviewNotice(`Theme preview: ${theme}`, 'info');
            
            // You could add actual preview functionality here
            // For now, just show a notice
        }

        /**
         * Preview position changes
         */
        previewPosition(e) {
            const position = $(e.target).val();
            
            // Create preview notice
            this.showPreviewNotice(`Position preview: ${position}`, 'info');
        }

        /**
         * Validate custom CSS
         */
        validateCustomCSS(e) {
            const $textarea = $(e.target);
            const css = $textarea.val();
            
            if (!css.trim()) {
                return true;
            }
            
            // Basic CSS validation
            const openBraces = (css.match(/{/g) || []).length;
            const closeBraces = (css.match(/}/g) || []).length;
            
            if (openBraces !== closeBraces) {
                this.showFieldError($textarea, 'CSS syntax error: mismatched braces');
                return false;
            }
            
            // Check for potentially dangerous CSS
            const dangerousPatterns = [
                /javascript:/i,
                /expression\(/i,
                /behavior:/i,
                /@import/i
            ];
            
            for (const pattern of dangerousPatterns) {
                if (pattern.test(css)) {
                    this.showFieldError($textarea, 'CSS contains potentially unsafe content');
                    return false;
                }
            }
            
            return true;
        }

        /**
         * Show field error
         */
        showFieldError($field, message) {
            $field.addClass('error');
            
            // Remove existing error
            $field.next('.field-error').remove();
            
            // Add error message
            $field.after(`<div class="field-error">${message}</div>`);
        }

        /**
         * Show validation errors
         */
        showValidationErrors() {
            if (!this.validationErrors || this.validationErrors.length === 0) {
                return;
            }
            
            const errorHtml = `
                <div class="notice notice-error is-dismissible">
                    <p><strong>Please fix the following errors:</strong></p>
                    <ul>
                        ${this.validationErrors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
            
            $('.wrap h1').after(errorHtml);
            
            // Scroll to top
            $('html, body').animate({ scrollTop: 0 }, 500);
        }

        /**
         * Show preview notice
         */
        showPreviewNotice(message, type = 'info') {
            // Remove existing preview notices
            $('.preview-notice').remove();
            
            const noticeHtml = `
                <div class="notice notice-${type} preview-notice is-dismissible">
                    <p>${message}</p>
                </div>
            `;
            
            $('.wrap h1').after(noticeHtml);
            
            // Auto-remove after 3 seconds
            setTimeout(() => {
                $('.preview-notice').fadeOut();
            }, 3000);
        }

        /**
         * Show loading state
         */
        showLoadingState($form) {
            $form.find('input[type="submit"]').prop('disabled', true).after('<span class="spinner is-active"></span>');
            $form.addClass('loading');
        }

        /**
         * Get field label
         */
        getFieldLabel($field) {
            const $label = $field.closest('tr').find('th label, th');
            return $label.text().replace(':', '').trim() || $field.attr('name');
        }

        /**
         * Initialize tooltips
         */
        initTooltips() {
            // Add tooltips to help icons
            $('.help-icon').each((index, element) => {
                const $icon = $(element);
                const helpText = $icon.data('help');
                
                if (helpText) {
                    $icon.attr('title', helpText);
                }
            });
            
            // Initialize WordPress-style tooltips if available
            if (typeof $.fn.tooltip === 'function') {
                $('.help-icon').tooltip();
            }
        }

        /**
         * Toggle help text
         */
        toggleHelp(e) {
            e.preventDefault();
            
            const $toggle = $(e.target);
            const $helpText = $toggle.next('.help-text');
            
            $helpText.slideToggle();
            $toggle.toggleClass('active');
        }

        /**
         * Check API configuration
         */
        checkApiConfiguration() {
            // This would typically make an AJAX call to check API status
            // For now, we'll just check if the warning notice is present
            
            if ($('.notice-warning').length > 0) {
                this.showConfigurationWarning();
            }
        }

        /**
         * Show configuration warning
         */
        showConfigurationWarning() {
            // Add visual indicators to highlight configuration issues
            $('.notice-warning').addClass('configuration-warning');
            
            // Add pulsing effect to draw attention
            $('.configuration-warning').css('animation', 'pulse 2s infinite');
        }

        /**
         * Export settings (placeholder)
         */
        exportSettings(e) {
            e.preventDefault();
            
            // Collect all settings
            const settings = {};
            $('input, textarea, select').each((index, element) => {
                const $element = $(element);
                const name = $element.attr('name');
                
                if (name && name.startsWith('openai_chatbot_')) {
                    if ($element.attr('type') === 'checkbox') {
                        settings[name] = $element.is(':checked');
                    } else {
                        settings[name] = $element.val();
                    }
                }
            });
            
            // Create download
            const dataStr = JSON.stringify(settings, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'openai-chatbot-settings.json';
            link.click();
            
            URL.revokeObjectURL(url);
        }

        /**
         * Import settings (placeholder)
         */
        importSettings(e) {
            e.preventDefault();
            
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = (event) => {
                const file = event.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const settings = JSON.parse(e.target.result);
                        this.applyImportedSettings(settings);
                    } catch (error) {
                        alert('Invalid settings file');
                    }
                };
                reader.readAsText(file);
            };
            
            input.click();
        }

        /**
         * Apply imported settings
         */
        applyImportedSettings(settings) {
            Object.keys(settings).forEach(name => {
                const $element = $(`[name="${name}"]`);
                
                if ($element.length) {
                    if ($element.attr('type') === 'checkbox') {
                        $element.prop('checked', settings[name]);
                    } else {
                        $element.val(settings[name]);
                    }
                }
            });
            
            this.showPreviewNotice('Settings imported successfully', 'success');
        }

        /**
         * Reset to defaults
         */
        resetToDefaults(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
                return;
            }
            
            // Reset form to default values
            // This would typically involve an AJAX call to get defaults
            location.reload();
        }
    }

    /**
     * Initialize admin when DOM is ready
     */
    $(document).ready(() => {
        if ($('.openai-chatbot-admin').length > 0) {
            new OpenAIChatbotAdmin();
        }
    });

    /**
     * Handle notice dismissals
     */
    $(document).on('click', '.notice-dismiss', function() {
        $(this).closest('.notice').fadeOut();
    });

    /**
     * Add CSS animations
     */
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .field-error {
            color: #d63638;
            font-size: 12px;
            margin-top: 5px;
            font-style: italic;
        }
        
        .error {
            border-color: #d63638 !important;
            box-shadow: 0 0 2px rgba(214, 54, 56, 0.8);
        }
        
        .character-counter {
            font-size: 11px;
            color: #646970;
            margin-top: 5px;
            text-align: right;
        }
        
        .character-counter.warning {
            color: #dba617;
        }
        
        .character-counter.error {
            color: #d63638;
            font-weight: bold;
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .disabled {
            opacity: 0.5;
        }
        
        .preview-notice {
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);

})(jQuery);