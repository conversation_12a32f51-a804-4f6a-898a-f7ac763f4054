<?php
/**
 * Admin Settings Page
 * 
 * Provides the WordPress admin interface for configuring the OpenAI Chatbot plugin
 * Includes settings for API configuration, appearance, behavior, and security
 * 
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['openai_chatbot_settings_nonce'], 'openai_chatbot_settings')) {
    // Save settings
    $settings = array(
        'openai_chatbot_enabled' => isset($_POST['openai_chatbot_enabled']),
        'openai_chatbot_position' => sanitize_text_field($_POST['openai_chatbot_position']),
        'openai_chatbot_theme' => sanitize_text_field($_POST['openai_chatbot_theme']),
        'openai_chatbot_bot_name' => sanitize_text_field($_POST['openai_chatbot_bot_name']),
        'openai_chatbot_welcome_message' => wp_kses_post($_POST['openai_chatbot_welcome_message']),
        'openai_chatbot_placeholder' => sanitize_text_field($_POST['openai_chatbot_placeholder']),
        'openai_chatbot_powered_by_text' => sanitize_text_field($_POST['openai_chatbot_powered_by_text']),
        'openai_chatbot_show_timestamps' => isset($_POST['openai_chatbot_show_timestamps']),
        'openai_chatbot_max_message_length' => absint($_POST['openai_chatbot_max_message_length']),
        'openai_chatbot_max_messages' => absint($_POST['openai_chatbot_max_messages']),
        'openai_chatbot_rate_limit_anonymous' => absint($_POST['openai_chatbot_rate_limit_anonymous']),
        'openai_chatbot_rate_limit_user' => absint($_POST['openai_chatbot_rate_limit_user']),
        'openai_chatbot_rate_limit_global' => absint($_POST['openai_chatbot_rate_limit_global']),
        'openai_chatbot_burst_limit' => absint($_POST['openai_chatbot_burst_limit']),
        'openai_chatbot_disabled_on' => isset($_POST['openai_chatbot_disabled_on']) ? array_map('sanitize_text_field', $_POST['openai_chatbot_disabled_on']) : array(),
        'openai_chatbot_custom_css' => wp_strip_all_tags($_POST['openai_chatbot_custom_css']),
        'openai_chatbot_add_structured_data' => isset($_POST['openai_chatbot_add_structured_data'])
    );

    foreach ($settings as $option => $value) {
        update_option($option, $value);
    }

    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'openai-chatbot') . '</p></div>';
}

// Test API connection
if (isset($_POST['test_api']) && wp_verify_nonce($_POST['openai_chatbot_test_nonce'], 'openai_chatbot_test')) {
    try {
        $api_handler = new OpenAI_Chatbot_API();
        $test_result = $api_handler->test_connection();
        
        if ($test_result['success']) {
            echo '<div class="notice notice-success"><p>' . __('API connection successful!', 'openai-chatbot') . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . __('API connection failed: ', 'openai-chatbot') . esc_html($test_result['message']) . '</p></div>';
        }
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>' . __('API connection failed: ', 'openai-chatbot') . esc_html($e->getMessage()) . '</p></div>';
    }
}

// Get current settings
$enabled = get_option('openai_chatbot_enabled', true);
$position = get_option('openai_chatbot_position', 'bottom-right');
$theme = get_option('openai_chatbot_theme', 'default');
$bot_name = get_option('openai_chatbot_bot_name', __('AI Assistant', 'openai-chatbot'));
$welcome_message = get_option('openai_chatbot_welcome_message', __('Hello! How can I help you today?', 'openai-chatbot'));
$placeholder = get_option('openai_chatbot_placeholder', __('Type your message...', 'openai-chatbot'));
$powered_by_text = get_option('openai_chatbot_powered_by_text', __('Powered by OpenAI', 'openai-chatbot'));
$show_timestamps = get_option('openai_chatbot_show_timestamps', false);
$max_message_length = get_option('openai_chatbot_max_message_length', 1000);
$max_messages = get_option('openai_chatbot_max_messages', 50);
$rate_limit_anonymous = get_option('openai_chatbot_rate_limit_anonymous', 5);
$rate_limit_user = get_option('openai_chatbot_rate_limit_user', 10);
$rate_limit_global = get_option('openai_chatbot_rate_limit_global', 100);
$burst_limit = get_option('openai_chatbot_burst_limit', 3);
$disabled_on = get_option('openai_chatbot_disabled_on', array());
$custom_css = get_option('openai_chatbot_custom_css', '');
$add_structured_data = get_option('openai_chatbot_add_structured_data', false);

// Check API configuration
$api_configured = defined('OPENAI_API_KEY') && !empty(OPENAI_API_KEY);
$assistant_configured = defined('OPENAI_ASSISTANT_ID') && !empty(OPENAI_ASSISTANT_ID);
?>

<div class="wrap">
    <h1><?php esc_html_e('OpenAI Chatbot Settings', 'openai-chatbot'); ?></h1>
    
    <?php if (!$api_configured || !$assistant_configured): ?>
        <div class="notice notice-warning">
            <p><strong><?php esc_html_e('Configuration Required', 'openai-chatbot'); ?></strong></p>
            <?php if (!$api_configured): ?>
                <p><?php esc_html_e('Please add your OpenAI API key to wp-config.php:', 'openai-chatbot'); ?></p>
                <code>define('OPENAI_API_KEY', 'your-api-key-here');</code>
            <?php endif; ?>
            <?php if (!$assistant_configured): ?>
                <p><?php esc_html_e('Please add your OpenAI Assistant ID to wp-config.php:', 'openai-chatbot'); ?></p>
                <code>define('OPENAI_ASSISTANT_ID', 'your-assistant-id-here');</code>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="openai-chatbot-admin">
        <div class="admin-content">
            <!-- Main Settings Form -->
            <form method="post" action="">
                <?php wp_nonce_field('openai_chatbot_settings', 'openai_chatbot_settings_nonce'); ?>
                
                <!-- General Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('General Settings', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Enable Chatbot', 'openai-chatbot'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="openai_chatbot_enabled" value="1" <?php checked($enabled); ?>>
                                        <?php esc_html_e('Enable the chatbot on your website', 'openai-chatbot'); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Bot Name', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_bot_name" value="<?php echo esc_attr($bot_name); ?>" class="regular-text">
                                    <p class="description"><?php esc_html_e('The name displayed in the chat header', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Welcome Message', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_welcome_message" rows="3" class="large-text"><?php echo esc_textarea($welcome_message); ?></textarea>
                                    <p class="description"><?php esc_html_e('The first message users see when opening the chat', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Input Placeholder', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_placeholder" value="<?php echo esc_attr($placeholder); ?>" class="regular-text">
                                    <p class="description"><?php esc_html_e('Placeholder text in the message input field', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Powered By Text', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="text" name="openai_chatbot_powered_by_text" value="<?php echo esc_attr($powered_by_text); ?>" class="regular-text">
                                    <p class="description"><?php esc_html_e('Text displayed in the chat footer', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Appearance', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Position', 'openai-chatbot'); ?></th>
                                <td>
                                    <select name="openai_chatbot_position">
                                        <option value="bottom-right" <?php selected($position, 'bottom-right'); ?>><?php esc_html_e('Bottom Right', 'openai-chatbot'); ?></option>
                                        <option value="bottom-left" <?php selected($position, 'bottom-left'); ?>><?php esc_html_e('Bottom Left', 'openai-chatbot'); ?></option>
                                        <option value="top-right" <?php selected($position, 'top-right'); ?>><?php esc_html_e('Top Right', 'openai-chatbot'); ?></option>
                                        <option value="top-left" <?php selected($position, 'top-left'); ?>><?php esc_html_e('Top Left', 'openai-chatbot'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Theme', 'openai-chatbot'); ?></th>
                                <td>
                                    <select name="openai_chatbot_theme">
                                        <option value="default" <?php selected($theme, 'default'); ?>><?php esc_html_e('Default', 'openai-chatbot'); ?></option>
                                        <option value="dark" <?php selected($theme, 'dark'); ?>><?php esc_html_e('Dark', 'openai-chatbot'); ?></option>
                                        <option value="minimal" <?php selected($theme, 'minimal'); ?>><?php esc_html_e('Minimal', 'openai-chatbot'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Show Timestamps', 'openai-chatbot'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="openai_chatbot_show_timestamps" value="1" <?php checked($show_timestamps); ?>>
                                        <?php esc_html_e('Display timestamps on messages', 'openai-chatbot'); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Custom CSS', 'openai-chatbot'); ?></th>
                                <td>
                                    <textarea name="openai_chatbot_custom_css" rows="10" class="large-text code"><?php echo esc_textarea($custom_css); ?></textarea>
                                    <p class="description"><?php esc_html_e('Add custom CSS to style the chatbot (advanced users only)', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Behavior Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Behavior', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Max Message Length', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_max_message_length" value="<?php echo esc_attr($max_message_length); ?>" min="100" max="2000" class="small-text">
                                    <p class="description"><?php esc_html_e('Maximum characters allowed per message', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Max Messages in History', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_max_messages" value="<?php echo esc_attr($max_messages); ?>" min="10" max="200" class="small-text">
                                    <p class="description"><?php esc_html_e('Maximum messages to keep in conversation history', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Disable On', 'openai-chatbot'); ?></th>
                                <td>
                                    <fieldset>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="home" <?php checked(in_array('home', $disabled_on)); ?>>
                                            <?php esc_html_e('Home Page', 'openai-chatbot'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="posts" <?php checked(in_array('posts', $disabled_on)); ?>>
                                            <?php esc_html_e('Blog Posts', 'openai-chatbot'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="pages" <?php checked(in_array('pages', $disabled_on)); ?>>
                                            <?php esc_html_e('Pages', 'openai-chatbot'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="openai_chatbot_disabled_on[]" value="archive" <?php checked(in_array('archive', $disabled_on)); ?>>
                                            <?php esc_html_e('Archive Pages', 'openai-chatbot'); ?>
                                        </label>
                                    </fieldset>
                                    <p class="description"><?php esc_html_e('Select page types where the chatbot should be disabled', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Security & Rate Limiting -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Security & Rate Limiting', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Anonymous User Limit', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_rate_limit_anonymous" value="<?php echo esc_attr($rate_limit_anonymous); ?>" min="1" max="50" class="small-text">
                                    <span><?php esc_html_e('messages per minute', 'openai-chatbot'); ?></span>
                                    <p class="description"><?php esc_html_e('Rate limit for non-logged-in users', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Logged-in User Limit', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_rate_limit_user" value="<?php echo esc_attr($rate_limit_user); ?>" min="1" max="100" class="small-text">
                                    <span><?php esc_html_e('messages per minute', 'openai-chatbot'); ?></span>
                                    <p class="description"><?php esc_html_e('Rate limit for logged-in users', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Global Limit', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_rate_limit_global" value="<?php echo esc_attr($rate_limit_global); ?>" min="10" max="1000" class="small-text">
                                    <span><?php esc_html_e('messages per minute', 'openai-chatbot'); ?></span>
                                    <p class="description"><?php esc_html_e('Global rate limit for all users combined', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php esc_html_e('Burst Limit', 'openai-chatbot'); ?></th>
                                <td>
                                    <input type="number" name="openai_chatbot_burst_limit" value="<?php echo esc_attr($burst_limit); ?>" min="1" max="10" class="small-text">
                                    <span><?php esc_html_e('messages in 10 seconds', 'openai-chatbot'); ?></span>
                                    <p class="description"><?php esc_html_e('Maximum rapid-fire messages allowed', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Advanced Settings -->
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Advanced', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php esc_html_e('Structured Data', 'openai-chatbot'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="openai_chatbot_add_structured_data" value="1" <?php checked($add_structured_data); ?>>
                                        <?php esc_html_e('Add structured data for SEO', 'openai-chatbot'); ?>
                                    </label>
                                    <p class="description"><?php esc_html_e('Adds JSON-LD structured data to help search engines understand your chatbot', 'openai-chatbot'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php submit_button(__('Save Settings', 'openai-chatbot')); ?>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="admin-sidebar">
            <!-- API Test -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('API Connection Test', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <p><?php esc_html_e('Test your OpenAI API connection to ensure everything is working correctly.', 'openai-chatbot'); ?></p>
                    <form method="post" action="">
                        <?php wp_nonce_field('openai_chatbot_test', 'openai_chatbot_test_nonce'); ?>
                        <input type="submit" name="test_api" class="button button-secondary" value="<?php esc_attr_e('Test API Connection', 'openai-chatbot'); ?>">
                    </form>
                </div>
            </div>

            <!-- Usage Instructions -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('Usage Instructions', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <h4><?php esc_html_e('Shortcode', 'openai-chatbot'); ?></h4>
                    <p><?php esc_html_e('Use this shortcode to embed the chatbot inline:', 'openai-chatbot'); ?></p>
                    <code>[openai_chatbot]</code>
                    
                    <h4><?php esc_html_e('Shortcode Options', 'openai-chatbot'); ?></h4>
                    <ul>
                        <li><code>height="400px"</code> - <?php esc_html_e('Set height', 'openai-chatbot'); ?></li>
                        <li><code>width="100%"</code> - <?php esc_html_e('Set width', 'openai-chatbot'); ?></li>
                    </ul>
                    
                    <h4><?php esc_html_e('Example', 'openai-chatbot'); ?></h4>
                    <code>[openai_chatbot height="500px" width="80%"]</code>
                </div>
            </div>

            <!-- Statistics -->
            <?php if (class_exists('OpenAI_Chatbot_Rate_Limiter')): ?>
                <?php
                $rate_limiter = new OpenAI_Chatbot_Rate_Limiter();
                $stats = $rate_limiter->get_statistics();
                ?>
                <div class="postbox">
                    <div class="postbox-header">
                        <h2 class="hndle"><?php esc_html_e('Usage Statistics', 'openai-chatbot'); ?></h2>
                    </div>
                    <div class="inside">
                        <table class="widefat">
                            <tr>
                                <td><?php esc_html_e('Active Rate Limits', 'openai-chatbot'); ?></td>
                                <td><strong><?php echo esc_html($stats['active_limits']); ?></strong></td>
                            </tr>
                            <tr>
                                <td><?php esc_html_e('Blocked Clients', 'openai-chatbot'); ?></td>
                                <td><strong><?php echo esc_html($stats['blocked_clients']); ?></strong></td>
                            </tr>
                            <tr>
                                <td><?php esc_html_e('Current Window Requests', 'openai-chatbot'); ?></td>
                                <td><strong><?php echo esc_html($stats['global_requests_current_window']); ?>/<?php echo esc_html($stats['global_limit']); ?></strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Support -->
            <div class="postbox">
                <div class="postbox-header">
                    <h2 class="hndle"><?php esc_html_e('Support', 'openai-chatbot'); ?></h2>
                </div>
                <div class="inside">
                    <p><?php esc_html_e('Need help? Check out these resources:', 'openai-chatbot'); ?></p>
                    <ul>
                        <li><a href="#" target="_blank"><?php esc_html_e('Documentation', 'openai-chatbot'); ?></a></li>
                        <li><a href="#" target="_blank"><?php esc_html_e('Support Forum', 'openai-chatbot'); ?></a></li>
                        <li><a href="https://platform.openai.com/docs" target="_blank"><?php esc_html_e('OpenAI API Docs', 'openai-chatbot'); ?></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.openai-chatbot-admin {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.admin-content {
    flex: 2;
}

.admin-sidebar {
    flex: 1;
    max-width: 300px;
}

.admin-sidebar .postbox {
    margin-bottom: 20px;
}

.admin-sidebar .inside ul {
    margin: 10px 0;
    padding-left: 20px;
}

.admin-sidebar .inside li {
    margin-bottom: 5px;
}

.admin-sidebar code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.admin-sidebar table.widefat td {
    padding: 8px 10px;
}

@media (max-width: 1200px) {
    .openai-chatbot-admin {
        flex-direction: column;
    }
    
    .admin-sidebar {
        max-width: none;
    }
}
</style>