# OpenAI Chatbot Plugin - Installation & Configuration Guide

This comprehensive guide will walk you through the complete setup process for the OpenAI Chatbot WordPress plugin.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation Methods](#installation-methods)
3. [OpenAI API Setup](#openai-api-setup)
4. [WordPress Configuration](#wordpress-configuration)
5. [Plugin Settings](#plugin-settings)
6. [Testing & Verification](#testing--verification)
7. [Troubleshooting](#troubleshooting)
8. [Performance Optimization](#performance-optimization)
9. [Security Considerations](#security-considerations)

## Prerequisites

### System Requirements

- **WordPress Version**: 5.0 or higher
- **PHP Version**: 7.4 or higher (8.0+ recommended)
- **MySQL Version**: 5.6 or higher
- **SSL Certificate**: Recommended for security
- **Memory Limit**: 128MB minimum (256MB recommended)
- **Max Execution Time**: 30 seconds minimum

### Required Accounts & Services

- OpenAI API account with active subscription
- WordPress admin access
- FTP/SFTP access (for manual installation)

### Technical Knowledge

- Basic WordPress administration
- Understanding of wp-config.php file
- Basic PHP/HTML knowledge (helpful but not required)

## Installation Methods

### Method 1: WordPress Admin Dashboard (Recommended)

1. **Download the Plugin**
   - Download the plugin ZIP file
   - Ensure the file is named `openai-chatbot.zip`

2. **Upload via WordPress Admin**
   ```
   WordPress Admin → Plugins → Add New → Upload Plugin
   ```
   - Click "Choose File" and select the ZIP file
   - Click "Install Now"
   - Click "Activate Plugin"

3. **Verify Installation**
   - Go to `Plugins → Installed Plugins`
   - Confirm "OpenAI Chatbot" is listed and active

### Method 2: FTP/SFTP Upload

1. **Extract Plugin Files**
   ```bash
   unzip openai-chatbot.zip
   ```

2. **Upload to WordPress**
   ```
   Upload the 'openai-chatbot' folder to:
   /wp-content/plugins/
   ```

3. **Set Permissions**
   ```bash
   chmod 755 /wp-content/plugins/openai-chatbot/
   chmod 644 /wp-content/plugins/openai-chatbot/*.php
   ```

4. **Activate Plugin**
   - Go to WordPress Admin → Plugins
   - Find "OpenAI Chatbot" and click "Activate"

### Method 3: WP-CLI Installation

```bash
# Upload and extract plugin
wp plugin install openai-chatbot.zip

# Activate plugin
wp plugin activate openai-chatbot

# Verify installation
wp plugin list --status=active
```

## OpenAI API Setup

### Step 1: Create OpenAI Account

1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to the API section
4. Set up billing (required for API access)

### Step 2: Generate API Key

1. Go to [API Keys](https://platform.openai.com/api-keys)
2. Click "Create new secret key"
3. Name your key (e.g., "WordPress Chatbot")
4. Copy the key immediately (it won't be shown again)
5. Store the key securely

**Important**: Never share your API key or commit it to version control.

### Step 3: Create OpenAI Assistant

1. Go to [OpenAI Assistants](https://platform.openai.com/assistants)
2. Click "Create Assistant"
3. Configure your assistant:
   ```
   Name: Website Assistant
   Instructions: You are a helpful assistant for [Your Website Name]. 
                Be friendly, concise, and helpful. Answer questions about 
                the website and provide general assistance.
   Model: gpt-4-turbo-preview (recommended)
   Tools: None (or add as needed)
   ```
4. Save the assistant
5. Copy the Assistant ID (starts with `asst_`)

### Step 4: Set Usage Limits (Recommended)

1. Go to [Usage Limits](https://platform.openai.com/account/limits)
2. Set monthly spending limits
3. Configure usage alerts
4. Monitor usage regularly

## WordPress Configuration

### Step 1: Configure wp-config.php

Add the following constants to your `wp-config.php` file (before the "That's all, stop editing!" line):

```php
// OpenAI Chatbot Configuration
define('OPENAI_API_KEY', 'your-openai-api-key-here');
define('OPENAI_ASSISTANT_ID', 'your-assistant-id-here');

// Optional: Enable debug mode for troubleshooting
define('OPENAI_CHATBOT_DEBUG', false);
```

**Security Note**: Ensure your wp-config.php file has proper permissions (600 or 644).

### Step 2: Verify Configuration

1. Go to WordPress Admin → Settings → OpenAI Chatbot
2. Check for configuration warnings
3. Click "Test API Connection" to verify setup

### Step 3: Database Setup (Automatic)

The plugin automatically creates necessary database tables:
- `wp_openai_chatbot_conversations` - Stores conversation metadata

## Plugin Settings

### General Settings

Navigate to `WordPress Admin → Settings → OpenAI Chatbot`

#### Basic Configuration

```
✓ Enable Chatbot: Checked
Bot Name: AI Assistant
Welcome Message: Hello! How can I help you today?
Input Placeholder: Type your message...
Powered By Text: Powered by OpenAI
```

#### Appearance Settings

```
Position: Bottom Right
Theme: Default
☐ Show Timestamps
Custom CSS: (leave blank initially)
```

#### Behavior Settings

```
Max Message Length: 1000
Max Messages in History: 50
Disable On: (select as needed)
  ☐ Home Page
  ☐ Blog Posts
  ☐ Pages
  ☐ Archive Pages
```

#### Security & Rate Limiting

```
Anonymous User Limit: 5 messages per minute
Logged-in User Limit: 10 messages per minute
Global Limit: 100 messages per minute
Burst Limit: 3 messages in 10 seconds
```

#### Advanced Settings

```
☐ Add Structured Data for SEO
```

### Save Settings

Click "Save Settings" to apply your configuration.

## Testing & Verification

### Step 1: Basic Functionality Test

1. **Frontend Test**
   - Visit your website's frontend
   - Look for the chat widget in the bottom-right corner
   - Click to open the chat interface

2. **Send Test Message**
   - Type a simple message like "Hello"
   - Verify the message sends successfully
   - Check that you receive a response from the AI

3. **Check Console for Errors**
   - Open browser developer tools (F12)
   - Look for JavaScript errors in the console
   - Verify network requests are successful

### Step 2: Shortcode Test

1. **Create Test Page**
   - Go to Pages → Add New
   - Add the shortcode: `[openai_chatbot]`
   - Publish the page

2. **Verify Inline Chat**
   - Visit the test page
   - Confirm the inline chat interface appears
   - Test sending messages

### Step 3: Rate Limiting Test

1. **Test Rate Limits**
   - Send multiple messages quickly
   - Verify rate limiting kicks in
   - Check error messages are user-friendly

2. **Test Different User Types**
   - Test as anonymous user
   - Test as logged-in user
   - Verify different limits apply

### Step 4: Mobile Responsiveness

1. **Test Mobile Interface**
   - Open website on mobile device
   - Verify chat interface is responsive
   - Test full-screen mobile layout

2. **Test Touch Interactions**
   - Verify touch scrolling works
   - Test keyboard appearance on input focus
   - Check button tap targets are adequate

## Troubleshooting

### Common Issues

#### 1. Chat Widget Not Appearing

**Symptoms**: No chat widget visible on frontend

**Solutions**:
```php
// Check if plugin is active
wp plugin list --status=active

// Verify wp-config.php settings
grep -i "OPENAI" wp-config.php

// Check for JavaScript errors in browser console
// Clear cache if using caching plugins
```

#### 2. API Connection Failed

**Symptoms**: "API connection failed" error

**Solutions**:
```php
// Verify API key in wp-config.php
define('OPENAI_API_KEY', 'sk-...');

// Check OpenAI account status and billing
// Verify Assistant ID is correct
// Test API key with curl:
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.openai.com/v1/models
```

#### 3. Messages Not Sending

**Symptoms**: Messages appear to send but no response

**Solutions**:
```php
// Enable debug mode
define('OPENAI_CHATBOT_DEBUG', true);

// Check WordPress error logs
tail -f /path/to/wordpress/wp-content/debug.log

// Verify nonce and AJAX endpoints
// Check rate limiting settings
```

#### 4. Styling Issues

**Symptoms**: Chat widget appears broken or unstyled

**Solutions**:
```css
/* Check for CSS conflicts */
/* Verify theme compatibility */
/* Test with default WordPress theme */

/* Add to theme's style.css if needed: */
.openai-chatbot-widget {
    z-index: 999999 !important;
}
```

### Debug Mode

Enable debug mode for detailed logging:

```php
// Add to wp-config.php
define('OPENAI_CHATBOT_DEBUG', true);
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check logs at: `/wp-content/debug.log`

### Performance Issues

#### High Memory Usage

```php
// Increase memory limit in wp-config.php
ini_set('memory_limit', '256M');

// Or in .htaccess
php_value memory_limit 256M
```

#### Slow Response Times

```php
// Check OpenAI API status
// Verify server response times
// Consider implementing caching for common responses
// Optimize database queries
```

## Performance Optimization

### Caching Strategy

1. **Response Caching**
   ```php
   // Implement in future version
   // Cache common responses for 1 hour
   set_transient('chatbot_response_' . md5($message), $response, HOUR_IN_SECONDS);
   ```

2. **Asset Optimization**
   ```php
   // Minify CSS/JS files
   // Use CDN for static assets
   // Implement lazy loading
   ```

### Database Optimization

1. **Regular Cleanup**
   ```sql
   -- Clean old conversation data (run monthly)
   DELETE FROM wp_openai_chatbot_conversations 
   WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
   ```

2. **Index Optimization**
   ```sql
   -- Ensure proper indexes exist
   SHOW INDEX FROM wp_openai_chatbot_conversations;
   ```

### Server Configuration

1. **PHP Settings**
   ```ini
   max_execution_time = 60
   memory_limit = 256M
   post_max_size = 10M
   upload_max_filesize = 10M
   ```

2. **Web Server**
   ```apache
   # Apache .htaccess
   <IfModule mod_gzip.c>
       mod_gzip_on Yes
       mod_gzip_dechunk Yes
   </IfModule>
   ```

## Security Considerations

### API Key Security

1. **Never expose API keys**
   - Store in wp-config.php only
   - Never commit to version control
   - Use environment variables in production

2. **Regular Key Rotation**
   - Rotate API keys monthly
   - Monitor usage for anomalies
   - Set up usage alerts

### Input Validation

1. **Message Sanitization**
   - All inputs are sanitized automatically
   - XSS protection enabled
   - SQL injection prevention

2. **Rate Limiting**
   - Prevents API abuse
   - Protects against spam
   - Configurable limits

### WordPress Security

1. **File Permissions**
   ```bash
   chmod 644 wp-config.php
   chmod 755 wp-content/plugins/openai-chatbot/
   ```

2. **Regular Updates**
   - Keep WordPress updated
   - Update plugin regularly
   - Monitor security advisories

## Advanced Configuration

### Custom Functions

Add to your theme's `functions.php`:

```php
// Customize system instructions
add_filter('openai_chatbot_system_instructions', function($instructions) {
    return $instructions . ' Always mention our 24/7 support hotline: 1-800-HELP.';
});

// Handle custom function calls
add_filter('openai_chatbot_function_call', function($result, $function_name, $args) {
    if ($function_name === 'get_business_hours') {
        return json_encode([
            'monday_friday' => '9 AM - 6 PM',
            'weekend' => '10 AM - 4 PM'
        ]);
    }
    return $result;
}, 10, 3);
```

### Custom Styling

```css
/* Add to your theme's style.css */
.openai-chatbot-widget {
    --chatbot-primary-color: #your-brand-color;
    --chatbot-border-radius: 15px;
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
    .openai-chatbot-widget .chatbot-window {
        height: 100vh;
    }
}
```

### Integration with Other Plugins

```php
// WooCommerce integration example
add_filter('openai_chatbot_system_instructions', function($instructions) {
    if (class_exists('WooCommerce')) {
        $instructions .= ' You can help with product questions and order status.';
    }
    return $instructions;
});
```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Monitor API usage
   - Check error logs
   - Review conversation quality

2. **Monthly**
   - Clean up old conversation data
   - Review and adjust rate limits
   - Update API keys if needed

3. **Quarterly**
   - Review plugin settings
   - Test all functionality
   - Update documentation

### Monitoring

1. **API Usage**
   - Monitor OpenAI dashboard
   - Set up usage alerts
   - Track costs

2. **Performance**
   - Monitor response times
   - Check server resources
   - Review user feedback

### Backup Strategy

1. **Database Backup**
   ```bash
   # Backup conversation data
   mysqldump -u user -p database wp_openai_chatbot_conversations > chatbot_backup.sql
   ```

2. **Configuration Backup**
   ```bash
   # Backup wp-config.php (securely)
   cp wp-config.php wp-config.backup
   ```

## Support Resources

### Documentation
- [WordPress Codex](https://codex.wordpress.org/)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Plugin GitHub Repository](#)

### Community
- [WordPress Support Forums](https://wordpress.org/support/)
- [OpenAI Community](https://community.openai.com/)

### Professional Support
- Plugin support email: <EMAIL>
- Priority support available for premium users

---

**Need Help?** If you encounter issues not covered in this guide, please check the troubleshooting section or contact support with detailed information about your setup and the specific problem you're experiencing.