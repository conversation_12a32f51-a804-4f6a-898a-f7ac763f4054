# OpenAI Chatbot Plugin - Technical Architecture

This document provides a comprehensive overview of the technical architecture, design patterns, and implementation details of the OpenAI Chatbot WordPress plugin.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Data Flow Diagrams](#data-flow-diagrams)
3. [Component Architecture](#component-architecture)
4. [Security Architecture](#security-architecture)
5. [Database Design](#database-design)
6. [API Integration](#api-integration)
7. [Frontend Architecture](#frontend-architecture)
8. [Performance Considerations](#performance-considerations)
9. [Scalability Design](#scalability-design)
10. [Error Handling Strategy](#error-handling-strategy)

## Architecture Overview

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │  WordPress      │    │   OpenAI API    │
│   (JavaScript)  │◄──►│  Backend (PHP)  │◄──►│   (Assistants)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Local Storage  │    │   WordPress     │    │   Thread        │
│  (Persistence)  │    │   Database      │    │   Management    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Design Principles

1. **Separation of Concerns**: Clear separation between frontend, backend, and API layers
2. **Security First**: Multiple layers of security validation and sanitization
3. **Performance Optimized**: Efficient caching, rate limiting, and resource management
4. **Scalable Design**: Modular architecture supporting high-traffic websites
5. **WordPress Standards**: Follows WordPress coding standards and best practices

### Technology Stack

- **Backend**: PHP 7.4+ with WordPress hooks and filters
- **Frontend**: Vanilla JavaScript (ES6+) with jQuery compatibility
- **Styling**: CSS3 with CSS Variables for theming
- **API**: OpenAI Assistants API v2
- **Database**: MySQL with WordPress wpdb abstraction
- **Caching**: WordPress Transients API

## Data Flow Diagrams

### Message Processing Flow

```
User Input
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│ 1. Input Validation (Length, Content)                      │
│ 2. UI State Management (Loading, Errors)                   │
│ 3. Local Storage Update                                     │
│ 4. AJAX Request Preparation                                 │
└─────────────────────────────────────────────────────────────┘
    │
    ▼ HTTP POST /wp-json/openai-chatbot/v1/chat
┌─────────────────────────────────────────────────────────────┐
│                   WordPress Layer                           │
├─────────────────────────────────────────────────────────────┤
│ 1. REST API Endpoint Routing                               │
│ 2. Nonce Verification                                       │
│ 3. Rate Limiting Check                                      │
│ 4. Input Sanitization & Security Validation                │
│ 5. User Permission Check                                    │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                  OpenAI API Layer                           │
├─────────────────────────────────────────────────────────────┤
│ 1. Thread Management (Get/Create)                          │
│ 2. Message Addition to Thread                              │
│ 3. Assistant Run Creation                                   │
│ 4. Polling for Completion                                   │
│ 5. Response Extraction                                      │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                  Response Processing                        │
├─────────────────────────────────────────────────────────────┤
│ 1. Response Validation                                      │
│ 2. Content Formatting                                       │
│ 3. Conversation State Update                                │
│ 4. Caching (if applicable)                                  │
│ 5. JSON Response Generation                                 │
└─────────────────────────────────────────────────────────────┘
    │
    ▼ JSON Response
┌─────────────────────────────────────────────────────────────┐
│                  Frontend Update                            │
├─────────────────────────────────────────────────────────────┤
│ 1. Response Parsing                                         │
│ 2. Message Display                                          │
│ 3. UI State Reset                                           │
│ 4. Local Storage Update                                     │
│ 5. Auto-scroll to Bottom                                    │
└─────────────────────────────────────────────────────────────┘
```

### Security Validation Flow

```
Request Input
    │
    ▼
┌─────────────────┐
│ Nonce Check     │ ──► Invalid ──► 403 Forbidden
└─────────────────┘
    │ Valid
    ▼
┌─────────────────┐
│ Rate Limiting   │ ──► Exceeded ──► 429 Too Many Requests
└─────────────────┘
    │ Within Limits
    ▼
┌─────────────────┐
│ Input           │ ──► Malicious ──► 400 Bad Request
│ Sanitization    │
└─────────────────┘
    │ Clean
    ▼
┌─────────────────┐
│ Content         │ ──► Suspicious ──► Security Log + Block
│ Validation      │
└─────────────────┘
    │ Safe
    ▼
Process Request
```

## Component Architecture

### Core Components

#### 1. Main Plugin Class (`OpenAI_Chatbot_Plugin`)

**Responsibilities**:
- Plugin initialization and lifecycle management
- Hook registration and WordPress integration
- Asset loading and dependency management
- Settings management

**Key Methods**:
```php
public static function get_instance()           // Singleton pattern
private function init_hooks()                   // WordPress hooks setup
public function enqueue_frontend_assets()       // Asset loading
public function register_rest_routes()          // REST API setup
```

#### 2. API Handler (`OpenAI_Chatbot_API`)

**Responsibilities**:
- OpenAI API communication
- Thread and conversation management
- Response processing and error handling
- Function call handling

**Key Methods**:
```php
public function send_message($message, $conversation_id)
private function get_or_create_thread($conversation_id)
private function wait_for_completion($thread_id, $run_id)
private function make_api_request($method, $endpoint, $data)
```

#### 3. Rate Limiter (`OpenAI_Chatbot_Rate_Limiter`)

**Responsibilities**:
- Request rate limiting and throttling
- Client identification and tracking
- Abuse prevention and blocking
- Usage analytics

**Key Methods**:
```php
public function check_rate_limit($identifier)
private function get_client_identifier()
public function block_client($identifier, $duration, $reason)
public function get_statistics()
```

#### 4. Security Handler (`OpenAI_Chatbot_Security_Handler`)

**Responsibilities**:
- Input validation and sanitization
- Threat detection and prevention
- Security logging and monitoring
- Content filtering

**Key Methods**:
```php
public function validate_message($message)
private function check_suspicious_patterns($message)
private function log_security_threat($message, $threats)
public function verify_nonce($nonce, $action)
```

### Frontend Components

#### 1. Main Chatbot Class (`OpenAIChatbot`)

**Responsibilities**:
- UI state management
- User interaction handling
- API communication
- Local storage management

**Key Methods**:
```javascript
constructor(options)                    // Initialization
async sendMessage()                     // Message sending
addMessage(content, type)               // UI updates
saveConversationHistory()               // Persistence
```

#### 2. UI Components

**Chat Widget Structure**:
```html
<div class="openai-chatbot-widget">
  ├── <div class="chatbot-toggle">          <!-- Toggle button -->
  └── <div class="chatbot-window">          <!-- Main interface -->
      ├── <div class="chatbot-header">      <!-- Header with bot info -->
      ├── <div class="chatbot-messages">    <!-- Message container -->
      │   ├── <div class="messages-container">
      │   └── <div class="typing-indicator">
      └── <div class="chatbot-input">       <!-- Input area -->
          ├── <form class="input-container">
          └── <div class="input-footer">
```

## Security Architecture

### Multi-Layer Security Model

```
┌─────────────────────────────────────────────────────────────┐
│                    Layer 1: Input Validation                │
├─────────────────────────────────────────────────────────────┤
│ • Length validation (max 1000 chars)                       │
│ • Character encoding validation                             │
│ • HTML tag stripping                                        │
│ • Null byte removal                                         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Layer 2: Threat Detection                  │
├─────────────────────────────────────────────────────────────┤
│ • SQL injection pattern matching                           │
│ • XSS attempt detection                                     │
│ • Command injection prevention                              │
│ • Path traversal blocking                                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Layer 3: Rate Limiting                    │
├─────────────────────────────────────────────────────────────┤
│ • Per-IP rate limiting                                      │
│ • Per-user rate limiting                                    │
│ • Global rate limiting                                      │
│ • Burst protection                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                Layer 4: Authentication                      │
├─────────────────────────────────────────────────────────────┤
│ • WordPress nonce verification                              │
│ • User capability checks                                    │
│ • Origin validation                                         │
│ • Session validation                                        │
└─────────────────────────────────────────────────────────────┘
```

### Security Patterns

#### 1. Input Sanitization Pattern

```php
// Multi-stage sanitization
$message = str_replace(chr(0), '', $message);           // Remove null bytes
$message = str_replace(["\r\n", "\r"], "\n", $message); // Normalize line endings
$message = wp_kses($message, $allowed_html);            // Strip HTML
$message = sanitize_textarea_field($message);           // WordPress sanitization
$message = preg_replace('/\s+/', ' ', $message);        // Remove excess whitespace
$message = trim($message);                              // Trim edges
```

#### 2. Threat Detection Pattern

```php
// Pattern-based threat detection
foreach ($this->suspicious_patterns as $threat_type => $patterns) {
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $message)) {
            $this->log_security_threat($message, [$threat_type]);
            return false; // Block request
        }
    }
}
```

#### 3. Rate Limiting Pattern

```php
// Token bucket algorithm implementation
public function check_rate_limit($client_id) {
    $key = 'rate_limit_' . $client_id;
    $current_count = get_transient($key) ?: 0;
    
    if ($current_count >= $this->limit) {
        return false; // Rate limited
    }
    
    set_transient($key, $current_count + 1, $this->window);
    return true; // Allow request
}
```

## Database Design

### Table Structure

#### Conversations Table

```sql
CREATE TABLE wp_openai_chatbot_conversations (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    conversation_id varchar(255) NOT NULL,
    user_id bigint(20) DEFAULT NULL,
    user_ip varchar(45) NOT NULL,
    message_count int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY conversation_id (conversation_id),
    KEY user_id (user_id),
    KEY created_at (created_at)
);
```

### Transient Usage

The plugin uses WordPress transients for temporary data storage:

```php
// Rate limiting data
set_transient('openai_chatbot_limit_' . $client_id, $count, $window);

// Thread mapping
set_transient('openai_chatbot_thread_' . $conversation_id, $thread_id, DAY_IN_SECONDS);

// Security threats
set_transient('openai_chatbot_threat_' . $timestamp, $threat_data, DAY_IN_SECONDS);

// Blocked clients
set_transient('openai_chatbot_blocked_' . $client_id, $block_data, $duration);
```

### Data Retention Policy

```php
// Automatic cleanup of old data
public function cleanup_expired_data() {
    global $wpdb;
    
    // Remove expired transients
    $wpdb->query("DELETE FROM {$wpdb->options} 
                  WHERE option_name LIKE '_transient_timeout_openai_chatbot_%' 
                  AND option_value < UNIX_TIMESTAMP()");
    
    // Remove orphaned transients
    $wpdb->query("DELETE FROM {$wpdb->options} 
                  WHERE option_name LIKE '_transient_openai_chatbot_%' 
                  AND option_name NOT IN (...)");
}
```

## API Integration

### OpenAI Assistants API Integration

#### Thread Management

```php
// Create or retrieve thread
private function get_or_create_thread($conversation_id) {
    if ($conversation_id) {
        $thread_id = get_transient('openai_chatbot_thread_' . $conversation_id);
        if ($thread_id) return $thread_id;
    }
    
    // Create new thread
    $response = $this->make_api_request('POST', '/threads', []);
    $thread_id = $response['id'];
    
    // Cache thread ID
    if ($conversation_id) {
        set_transient('openai_chatbot_thread_' . $conversation_id, $thread_id, HOUR_IN_SECONDS);
    }
    
    return $thread_id;
}
```

#### Message Processing

```php
// Add message and run assistant
public function send_message($message, $conversation_id = null) {
    $thread_id = $this->get_or_create_thread($conversation_id);
    
    // Add user message
    $this->add_message_to_thread($thread_id, $message);
    
    // Create run
    $run_id = $this->create_run($thread_id);
    
    // Wait for completion
    $response = $this->wait_for_completion($thread_id, $run_id);
    
    return [
        'message' => $response,
        'conversation_id' => $conversation_id ?: $this->generate_conversation_id(),
        'thread_id' => $thread_id
    ];
}
```

#### Error Handling and Retry Logic

```php
// Exponential backoff retry
private function make_request_with_retry($url, $args) {
    $retries = 0;
    
    while ($retries < $this->max_retries) {
        $response = wp_remote_request($url, $args);
        
        if (!is_wp_error($response)) {
            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code < 500 && $status_code !== 429) {
                return $response;
            }
        }
        
        $retries++;
        if ($retries < $this->max_retries) {
            sleep(pow(2, $retries - 1)); // Exponential backoff
        }
    }
    
    return $response;
}
```

## Frontend Architecture

### State Management

```javascript
// Centralized state object
this.state = {
    isOpen: false,
    isLoading: false,
    conversationId: null,
    messageHistory: [],
    retryCount: 0,
    lastMessageTime: 0,
    typingTimer: null,
    connectionStatus: 'disconnected'
};
```

### Event-Driven Architecture

```javascript
// Event binding pattern
bindEvents() {
    // UI interactions
    this.elements.toggle.on('click', () => this.toggleChat());
    this.elements.sendButton.on('click', () => this.sendMessage());
    
    // Input handling
    this.elements.messageInput.on('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    });
    
    // Real-time updates
    this.elements.messageInput.on('input', () => {
        this.autoResizeTextarea();
        this.updateCharacterCount();
        this.updateSendButton();
    });
}
```

### Async/Await Pattern

```javascript
// Modern async handling
async sendMessage() {
    const message = this.elements.messageInput.val().trim();
    
    if (!message || this.state.isLoading) return;
    
    this.addMessage(message, 'user');
    this.setLoadingState(true);
    
    try {
        const response = await this.makeApiRequest(message);
        
        if (response.success) {
            this.addMessage(response.data.message, 'bot');
            this.state.conversationId = response.data.conversation_id;
        } else {
            throw new Error(response.data);
        }
    } catch (error) {
        this.handleApiError(error, message);
    } finally {
        this.setLoadingState(false);
        this.saveConversationHistory();
    }
}
```

## Performance Considerations

### Caching Strategy

#### 1. Response Caching

```php
// Cache common responses
$cache_key = 'chatbot_response_' . md5($message);
$cached_response = get_transient($cache_key);

if ($cached_response !== false) {
    return $cached_response;
}

// Generate response and cache
$response = $this->generate_response($message);
set_transient($cache_key, $response, HOUR_IN_SECONDS);
```

#### 2. Asset Optimization

```php
// Conditional asset loading
public function enqueue_frontend_assets() {
    if (!$this->should_load_chatbot()) {
        return;
    }
    
    // Minified assets in production
    $suffix = defined('SCRIPT_DEBUG') && SCRIPT_DEBUG ? '' : '.min';
    
    wp_enqueue_style(
        'openai-chatbot-style',
        OPENAI_CHATBOT_PLUGIN_URL . "assets/css/chatbot{$suffix}.css",
        [],
        OPENAI_CHATBOT_VERSION
    );
}
```

### Database Optimization

#### 1. Query Optimization

```php
// Efficient queries with proper indexing
$conversations = $wpdb->get_results($wpdb->prepare("
    SELECT conversation_id, message_count, created_at 
    FROM {$wpdb->prefix}openai_chatbot_conversations 
    WHERE user_id = %d 
    AND created_at > %s 
    ORDER BY created_at DESC 
    LIMIT %d
", $user_id, $date_threshold, $limit));
```

#### 2. Cleanup Routines

```php
// Scheduled cleanup
add_action('wp_scheduled_delete', function() {
    global $wpdb;
    
    // Clean old conversations
    $wpdb->query($wpdb->prepare("
        DELETE FROM {$wpdb->prefix}openai_chatbot_conversations 
        WHERE created_at < %s
    ", date('Y-m-d H:i:s', strtotime('-30 days'))));
});
```

### Memory Management

```php
// Memory-efficient processing
public function process_large_dataset($data) {
    // Process in chunks to avoid memory issues
    $chunks = array_chunk($data, 100);
    
    foreach ($chunks as $chunk) {
        $this->process_chunk($chunk);
        
        // Clear memory
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }
}
```

## Scalability Design

### Horizontal Scaling Considerations

#### 1. Stateless Design

```php
// No server-side session storage
// All state passed in requests or stored in database/cache
public function handle_request($request) {
    $conversation_id = $request->get_param('conversation_id');
    $thread_id = $this->get_thread_id($conversation_id);
    
    // Process without server state
    return $this->process_message($message, $thread_id);
}
```

#### 2. Database Connection Pooling

```php
// Efficient database usage
class Database_Manager {
    private static $connections = [];
    
    public static function get_connection($key = 'default') {
        if (!isset(self::$connections[$key])) {
            self::$connections[$key] = new wpdb(DB_USER, DB_PASSWORD, DB_NAME, DB_HOST);
        }
        return self::$connections[$key];
    }
}
```

### Load Balancing Support

```php
// Session-independent design
public function get_client_identifier() {
    // Use consistent hashing for load balancing
    $factors = [
        $this->get_client_ip(),
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        is_user_logged_in() ? get_current_user_id() : 'anonymous'
    ];
    
    return hash('sha256', implode('|', $factors));
}
```

## Error Handling Strategy

### Hierarchical Error Handling

```php
// Error handling hierarchy
try {
    $response = $this->send_message($message, $conversation_id);
} catch (OpenAI_API_Exception $e) {
    // API-specific errors
    $this->log_api_error($e);
    return $this->get_fallback_response();
} catch (Rate_Limit_Exception $e) {
    // Rate limiting errors
    return new WP_Error('rate_limited', $e->getMessage(), ['status' => 429]);
} catch (Security_Exception $e) {
    // Security violations
    $this->log_security_violation($e);
    return new WP_Error('security_violation', 'Request blocked', ['status' => 403]);
} catch (Exception $e) {
    // Generic errors
    $this->log_generic_error($e);
    return new WP_Error('internal_error', 'Internal server error', ['status' => 500]);
}
```

### Graceful Degradation

```javascript
// Frontend error handling with fallback
async makeApiRequest(message) {
    try {
        const response = await fetch(this.options.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': this.options.nonce
            },
            body: JSON.stringify({ message })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        // Fallback to basic error message
        return {
            success: false,
            data: this.strings.error || 'Something went wrong'
        };
    }
}
```

### Logging Strategy

```php
// Structured logging
public function log_error($level, $message, $context = []) {
    $log_entry = [
        'timestamp' => current_time('Y-m-d H:i:s'),
        'level' => $level,
        'message' => $message,
        'context' => $context,
        'user_id' => is_user_logged_in() ? get_current_user_id() : null,
        'ip' => $this->get_client_ip(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? ''
    ];
    
    error_log('OpenAI Chatbot [' . strtoupper($level) . ']: ' . json_encode($log_entry));
    
    // Store critical errors for admin review
    if (in_array($level, ['error', 'critical'])) {
        $this->store_admin_notice($log_entry);
    }
}
```

---

This technical architecture document provides a comprehensive overview of the plugin's design and implementation. It serves as a reference for developers working with or extending the plugin, ensuring maintainability and scalability of the codebase.