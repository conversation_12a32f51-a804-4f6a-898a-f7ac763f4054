<?php
/**
 * OpenAI API Integration Class
 * 
 * Handles all communication with OpenAI's Assistants API
 * Manages conversation threads, message processing, and error handling
 * 
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * OpenAI Chatbot API Handler
 * 
 * This class manages all interactions with the OpenAI Assistants API,
 * including thread management, message sending, and response processing.
 */
class OpenAI_Chatbot_API {

    /**
     * OpenAI API base URL
     * 
     * @var string
     */
    private $api_base_url = 'https://api.openai.com/v1';

    /**
     * OpenAI API key
     * 
     * @var string
     */
    private $api_key;

    /**
     * OpenAI Assistant ID
     * 
     * @var string
     */
    private $assistant_id;

    /**
     * HTTP timeout for API requests
     * 
     * @var int
     */
    private $timeout = 30;

    /**
     * Maximum retries for failed requests
     * 
     * @var int
     */
    private $max_retries = 3;

    /**
     * Constructor
     * 
     * Initialize API credentials and validate configuration
     */
    public function __construct() {
        $this->api_key = defined('OPENAI_API_KEY') ? OPENAI_API_KEY : '';
        $this->assistant_id = defined('OPENAI_ASSISTANT_ID') ? OPENAI_ASSISTANT_ID : '';

        // Validate API configuration
        if (empty($this->api_key)) {
            throw new Exception(__('OpenAI API key is not configured.', 'openai-chatbot'));
        }

        if (empty($this->assistant_id)) {
            throw new Exception(__('OpenAI Assistant ID is not configured.', 'openai-chatbot'));
        }
    }

    /**
     * Send message to OpenAI Assistant
     * 
     * @param string $message User message
     * @param string $conversation_id Optional conversation ID for thread continuity
     * @return array Response data including message and conversation ID
     * @throws Exception On API errors
     */
    public function send_message($message, $conversation_id = null) {
        try {
            // Get or create thread
            $thread_id = $this->get_or_create_thread($conversation_id);

            // Add message to thread
            $this->add_message_to_thread($thread_id, $message);

            // Run assistant on thread
            $run_id = $this->create_run($thread_id);

            // Wait for completion and get response
            $response_message = $this->wait_for_completion($thread_id, $run_id);

            // Store conversation mapping
            $this->store_conversation_mapping($conversation_id, $thread_id);

            return array(
                'message' => $response_message,
                'conversation_id' => $conversation_id ?: $this->generate_conversation_id(),
                'thread_id' => $thread_id,
                'timestamp' => current_time('timestamp')
            );

        } catch (Exception $e) {
            // Log detailed error for debugging
            if (defined('OPENAI_CHATBOT_DEBUG') && OPENAI_CHATBOT_DEBUG) {
                error_log('OpenAI API Error Details: ' . $e->getMessage());
            }

            // Re-throw with user-friendly message
            throw new Exception(__('Unable to process your message. Please try again.', 'openai-chatbot'));
        }
    }

    /**
     * Get existing thread or create new one
     * 
     * @param string $conversation_id Conversation identifier
     * @return string Thread ID
     */
    private function get_or_create_thread($conversation_id) {
        if ($conversation_id) {
            // Try to get existing thread ID from cache
            $thread_id = get_transient('openai_chatbot_thread_' . $conversation_id);
            if ($thread_id) {
                return $thread_id;
            }
        }

        // Create new thread
        $response = $this->make_api_request('POST', '/threads', array());
        
        if (!isset($response['id'])) {
            throw new Exception(__('Failed to create conversation thread.', 'openai-chatbot'));
        }

        $thread_id = $response['id'];

        // Cache thread ID for 1 hour
        if ($conversation_id) {
            set_transient('openai_chatbot_thread_' . $conversation_id, $thread_id, HOUR_IN_SECONDS);
        }

        return $thread_id;
    }

    /**
     * Add message to thread
     * 
     * @param string $thread_id Thread identifier
     * @param string $message User message
     * @throws Exception On API errors
     */
    private function add_message_to_thread($thread_id, $message) {
        $data = array(
            'role' => 'user',
            'content' => $message
        );

        $response = $this->make_api_request('POST', "/threads/{$thread_id}/messages", $data);

        if (!isset($response['id'])) {
            throw new Exception(__('Failed to add message to conversation.', 'openai-chatbot'));
        }
    }

    /**
     * Create and start assistant run
     * 
     * @param string $thread_id Thread identifier
     * @return string Run ID
     * @throws Exception On API errors
     */
    private function create_run($thread_id) {
        $data = array(
            'assistant_id' => $this->assistant_id,
            'instructions' => $this->get_system_instructions()
        );

        $response = $this->make_api_request('POST', "/threads/{$thread_id}/runs", $data);

        if (!isset($response['id'])) {
            throw new Exception(__('Failed to start assistant processing.', 'openai-chatbot'));
        }

        return $response['id'];
    }

    /**
     * Wait for run completion and get response
     * 
     * @param string $thread_id Thread identifier
     * @param string $run_id Run identifier
     * @return string Assistant response message
     * @throws Exception On timeout or API errors
     */
    private function wait_for_completion($thread_id, $run_id) {
        $max_wait_time = 30; // Maximum wait time in seconds
        $poll_interval = 1; // Poll every second
        $elapsed_time = 0;

        while ($elapsed_time < $max_wait_time) {
            $run_status = $this->get_run_status($thread_id, $run_id);

            switch ($run_status['status']) {
                case 'completed':
                    return $this->get_latest_assistant_message($thread_id);

                case 'failed':
                case 'cancelled':
                case 'expired':
                    throw new Exception(__('Assistant processing failed.', 'openai-chatbot'));

                case 'requires_action':
                    // Handle function calls if needed
                    $this->handle_required_actions($thread_id, $run_id, $run_status);
                    break;

                case 'queued':
                case 'in_progress':
                case 'cancelling':
                    // Continue waiting
                    break;

                default:
                    throw new Exception(__('Unknown assistant status.', 'openai-chatbot'));
            }

            sleep($poll_interval);
            $elapsed_time += $poll_interval;
        }

        throw new Exception(__('Assistant response timeout.', 'openai-chatbot'));
    }

    /**
     * Get run status
     * 
     * @param string $thread_id Thread identifier
     * @param string $run_id Run identifier
     * @return array Run status data
     */
    private function get_run_status($thread_id, $run_id) {
        return $this->make_api_request('GET', "/threads/{$thread_id}/runs/{$run_id}");
    }

    /**
     * Handle required actions (function calls)
     * 
     * @param string $thread_id Thread identifier
     * @param string $run_id Run identifier
     * @param array $run_status Current run status
     */
    private function handle_required_actions($thread_id, $run_id, $run_status) {
        if (!isset($run_status['required_action']['submit_tool_outputs']['tool_calls'])) {
            return;
        }

        $tool_outputs = array();
        $tool_calls = $run_status['required_action']['submit_tool_outputs']['tool_calls'];

        foreach ($tool_calls as $tool_call) {
            if ($tool_call['type'] === 'function') {
                $function_name = $tool_call['function']['name'];
                $function_args = json_decode($tool_call['function']['arguments'], true);
                
                // Execute function and get output
                $output = $this->execute_function($function_name, $function_args);
                
                $tool_outputs[] = array(
                    'tool_call_id' => $tool_call['id'],
                    'output' => $output
                );
            }
        }

        // Submit tool outputs
        if (!empty($tool_outputs)) {
            $this->submit_tool_outputs($thread_id, $run_id, $tool_outputs);
        }
    }

    /**
     * Execute function call
     * 
     * @param string $function_name Function name
     * @param array $args Function arguments
     * @return string Function output
     */
    private function execute_function($function_name, $args) {
        // Apply filters to allow custom function handling
        $result = apply_filters('openai_chatbot_function_call', null, $function_name, $args);
        
        if ($result !== null) {
            return $result;
        }

        // Default function handling
        switch ($function_name) {
            case 'get_current_time':
                return current_time('Y-m-d H:i:s');
                
            case 'get_website_info':
                return json_encode(array(
                    'site_name' => get_bloginfo('name'),
                    'site_url' => home_url(),
                    'description' => get_bloginfo('description')
                ));
                
            default:
                return json_encode(array('error' => 'Function not implemented'));
        }
    }

    /**
     * Submit tool outputs
     * 
     * @param string $thread_id Thread identifier
     * @param string $run_id Run identifier
     * @param array $tool_outputs Tool outputs
     */
    private function submit_tool_outputs($thread_id, $run_id, $tool_outputs) {
        $data = array('tool_outputs' => $tool_outputs);
        $this->make_api_request('POST', "/threads/{$thread_id}/runs/{$run_id}/submit_tool_outputs", $data);
    }

    /**
     * Get latest assistant message from thread
     * 
     * @param string $thread_id Thread identifier
     * @return string Assistant message
     * @throws Exception If no message found
     */
    private function get_latest_assistant_message($thread_id) {
        $response = $this->make_api_request('GET', "/threads/{$thread_id}/messages", array(
            'limit' => 1,
            'order' => 'desc'
        ));

        if (!isset($response['data'][0]['content'][0]['text']['value'])) {
            throw new Exception(__('No response received from assistant.', 'openai-chatbot'));
        }

        return $response['data'][0]['content'][0]['text']['value'];
    }

    /**
     * Make API request to OpenAI
     * 
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array Response data
     * @throws Exception On API errors
     */
    private function make_api_request($method, $endpoint, $data = array()) {
        $url = $this->api_base_url . $endpoint;
        
        $headers = array(
            'Authorization' => 'Bearer ' . $this->api_key,
            'Content-Type' => 'application/json',
            'OpenAI-Beta' => 'assistants=v2'
        );

        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => $this->timeout,
            'sslverify' => true
        );

        if (!empty($data) && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            $args['body'] = json_encode($data);
        } elseif (!empty($data) && $method === 'GET') {
            $url .= '?' . http_build_query($data);
        }

        // Make request with retry logic
        $response = $this->make_request_with_retry($url, $args);

        if (is_wp_error($response)) {
            throw new Exception('API request failed: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);

        if ($status_code >= 400) {
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['error']['message']) 
                ? $error_data['error']['message'] 
                : 'API request failed with status ' . $status_code;
            
            throw new Exception($error_message);
        }

        $decoded_response = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response from API');
        }

        return $decoded_response;
    }

    /**
     * Make HTTP request with retry logic
     * 
     * @param string $url Request URL
     * @param array $args Request arguments
     * @return array|WP_Error Response
     */
    private function make_request_with_retry($url, $args) {
        $retries = 0;
        
        while ($retries < $this->max_retries) {
            $response = wp_remote_request($url, $args);
            
            if (!is_wp_error($response)) {
                $status_code = wp_remote_retrieve_response_code($response);
                
                // Retry on server errors (5xx) and rate limits (429)
                if ($status_code < 500 && $status_code !== 429) {
                    return $response;
                }
            }
            
            $retries++;
            
            if ($retries < $this->max_retries) {
                // Exponential backoff: 1s, 2s, 4s
                sleep(pow(2, $retries - 1));
            }
        }
        
        return $response;
    }

    /**
     * Get system instructions for the assistant
     * 
     * @return string System instructions
     */
    private function get_system_instructions() {
        $default_instructions = sprintf(
            __('You are a helpful assistant for %s. Be concise, friendly, and helpful. If you don\'t know something, say so honestly.', 'openai-chatbot'),
            get_bloginfo('name')
        );

        // Allow customization via filter
        return apply_filters('openai_chatbot_system_instructions', $default_instructions);
    }

    /**
     * Generate unique conversation ID
     * 
     * @return string Conversation ID
     */
    private function generate_conversation_id() {
        return 'conv_' . wp_generate_uuid4();
    }

    /**
     * Store conversation to thread mapping
     * 
     * @param string $conversation_id Conversation ID
     * @param string $thread_id Thread ID
     */
    private function store_conversation_mapping($conversation_id, $thread_id) {
        if ($conversation_id) {
            set_transient('openai_chatbot_thread_' . $conversation_id, $thread_id, DAY_IN_SECONDS);
        }
    }

    /**
     * Test API connection
     * 
     * @return array Test results
     */
    public function test_connection() {
        try {
            // Test by creating a simple thread
            $response = $this->make_api_request('POST', '/threads', array());
            
            return array(
                'success' => true,
                'message' => __('API connection successful', 'openai-chatbot'),
                'thread_id' => $response['id'] ?? null
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }
}