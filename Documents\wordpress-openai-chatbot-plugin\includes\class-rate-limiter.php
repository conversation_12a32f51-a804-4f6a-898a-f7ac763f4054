<?php
/**
 * Rate Limiter Class
 * 
 * Implements rate limiting to prevent API abuse and protect against spam
 * Uses WordPress transients for storage with IP-based and user-based limits
 * 
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * OpenAI Chatbot Rate Limiter
 * 
 * This class implements sophisticated rate limiting to protect the OpenAI API
 * from abuse while providing a good user experience for legitimate users.
 */
class OpenAI_Chatbot_Rate_Limiter {

    /**
     * Default rate limit per minute for anonymous users
     * 
     * @var int
     */
    private $anonymous_limit = 50;

    /**
     * Default rate limit per minute for logged-in users
     * 
     * @var int
     */
    private $user_limit = 100;

    /**
     * Global rate limit per minute
     * 
     * @var int
     */
    private $global_limit = 100;

    /**
     * Time window for rate limiting (in seconds)
     * 
     * @var int
     */
    private $time_window = 60;

    /**
     * Burst limit - maximum requests in a short burst
     * 
     * @var int
     */
    private $burst_limit = 10;

    /**
     * Burst time window (in seconds)
     * 
     * @var int
     */
    private $burst_window = 60;

    /**
     * Constructor
     * 
     * Initialize rate limits from WordPress options
     */
    public function __construct() {
        $this->anonymous_limit = get_option('openai_chatbot_rate_limit_anonymous', 5);
        $this->user_limit = get_option('openai_chatbot_rate_limit_user', 10);
        $this->global_limit = get_option('openai_chatbot_rate_limit_global', 100);
        $this->burst_limit = get_option('openai_chatbot_burst_limit', 3);
    }

    /**
     * Check if request is within rate limits
     * 
     * @param string $identifier Optional custom identifier
     * @return bool True if within limits, false if rate limited
     */
    public function check_rate_limit($identifier = null) {
        // Get client identifier
        $client_id = $identifier ?: $this->get_client_identifier();
        
        error_log('OpenAI Chatbot Rate Limiter: Checking rate limit for client: ' . $client_id);
        
        // Check burst limit first (most restrictive)
        if (!$this->check_burst_limit($client_id)) {
            error_log('OpenAI Chatbot Rate Limiter: Burst limit exceeded for client: ' . $client_id);
            $this->log_rate_limit_violation($client_id, 'burst');
            return false;
        }

        // Check per-client rate limit
        if (!$this->check_client_limit($client_id)) {
            error_log('OpenAI Chatbot Rate Limiter: Client limit exceeded for client: ' . $client_id);
            $this->log_rate_limit_violation($client_id, 'client');
            return false;
        }

        // Check global rate limit
        if (!$this->check_global_limit()) {
            error_log('OpenAI Chatbot Rate Limiter: Global limit exceeded');
            $this->log_rate_limit_violation($client_id, 'global');
            return false;
        }

        // All checks passed, record the request
        error_log('OpenAI Chatbot Rate Limiter: Rate limit check passed for client: ' . $client_id);
        $this->record_request($client_id);
        
        return true;
    }

    /**
     * Check burst limit (short-term rapid requests)
     * 
     * @param string $client_id Client identifier
     * @return bool True if within burst limit
     */
    private function check_burst_limit($client_id) {
        $burst_key = 'openai_chatbot_burst_' . $client_id;
        $burst_count = get_transient($burst_key);
        
        if ($burst_count === false) {
            // No recent burst activity
            return true;
        }
        
        return $burst_count < $this->burst_limit;
    }

    /**
     * Check per-client rate limit
     * 
     * @param string $client_id Client identifier
     * @return bool True if within client limit
     */
    private function check_client_limit($client_id) {
        $limit_key = 'openai_chatbot_limit_' . $client_id;
        $request_count = get_transient($limit_key);
        
        if ($request_count === false) {
            // No recent activity
            return true;
        }
        
        $max_requests = is_user_logged_in() ? $this->user_limit : $this->anonymous_limit;
        
        return $request_count < $max_requests;
    }

    /**
     * Check global rate limit
     * 
     * @return bool True if within global limit
     */
    private function check_global_limit() {
        $global_key = 'openai_chatbot_global_limit';
        $global_count = get_transient($global_key);
        
        if ($global_count === false) {
            // No recent global activity
            return true;
        }
        
        return $global_count < $this->global_limit;
    }

    /**
     * Record a request for rate limiting
     * 
     * @param string $client_id Client identifier
     */
    private function record_request($client_id) {
        // Record burst activity
        $this->increment_counter('openai_chatbot_burst_' . $client_id, $this->burst_window);
        
        // Record client activity
        $this->increment_counter('openai_chatbot_limit_' . $client_id, $this->time_window);
        
        // Record global activity
        $this->increment_counter('openai_chatbot_global_limit', $this->time_window);
        
        // Store request metadata for analytics
        $this->store_request_metadata($client_id);
    }

    /**
     * Increment counter with expiration
     * 
     * @param string $key Transient key
     * @param int $expiration Expiration time in seconds
     */
    private function increment_counter($key, $expiration) {
        $count = get_transient($key);
        
        if ($count === false) {
            set_transient($key, 1, $expiration);
        } else {
            set_transient($key, $count + 1, $expiration);
        }
    }

    /**
     * Get client identifier for rate limiting
     * 
     * @return string Client identifier
     */
    private function get_client_identifier() {
        if (is_user_logged_in()) {
            // Use user ID for logged-in users
            return 'user_' . get_current_user_id();
        } else {
            // Use IP address for anonymous users
            return 'ip_' . $this->get_client_ip();
        }
    }

    /**
     * Get client IP address
     * 
     * @return string IP address
     */
    private function get_client_ip() {
        // Check for various headers that might contain the real IP
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Get remaining requests for client
     * 
     * @param string $identifier Optional custom identifier
     * @return array Rate limit status
     */
    public function get_rate_limit_status($identifier = null) {
        $client_id = $identifier ?: $this->get_client_identifier();
        
        // Get current counts
        $burst_count = get_transient('openai_chatbot_burst_' . $client_id) ?: 0;
        $client_count = get_transient('openai_chatbot_limit_' . $client_id) ?: 0;
        $global_count = get_transient('openai_chatbot_global_limit') ?: 0;
        
        $max_requests = is_user_logged_in() ? $this->user_limit : $this->anonymous_limit;
        
        return array(
            'burst_remaining' => max(0, $this->burst_limit - $burst_count),
            'client_remaining' => max(0, $max_requests - $client_count),
            'global_remaining' => max(0, $this->global_limit - $global_count),
            'reset_time' => time() + $this->time_window,
            'burst_reset_time' => time() + $this->burst_window
        );
    }

    /**
     * Check if client is currently blocked
     * 
     * @param string $identifier Optional custom identifier
     * @return bool True if blocked
     */
    public function is_blocked($identifier = null) {
        $client_id = $identifier ?: $this->get_client_identifier();
        $block_key = 'openai_chatbot_blocked_' . $client_id;
        
        return get_transient($block_key) !== false;
    }

    /**
     * Block a client for a specified duration
     * 
     * @param string $identifier Client identifier
     * @param int $duration Block duration in seconds
     * @param string $reason Block reason
     */
    public function block_client($identifier, $duration = 300, $reason = 'Rate limit exceeded') {
        $block_key = 'openai_chatbot_blocked_' . $identifier;
        $block_data = array(
            'blocked_at' => time(),
            'duration' => $duration,
            'reason' => $reason,
            'ip' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        );
        
        set_transient($block_key, $block_data, $duration);
        
        // Log the block
        $this->log_client_block($identifier, $reason, $duration);
    }

    /**
     * Unblock a client
     * 
     * @param string $identifier Client identifier
     */
    public function unblock_client($identifier) {
        $block_key = 'openai_chatbot_blocked_' . $identifier;
        delete_transient($block_key);
    }

    /**
     * Get blocked clients list
     * 
     * @return array List of blocked clients
     */
    public function get_blocked_clients() {
        global $wpdb;
        
        $blocked_clients = array();
        
        // Query transients table for blocked clients
        $results = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_openai_chatbot_blocked_%'",
            ARRAY_A
        );
        
        foreach ($results as $result) {
            $client_id = str_replace('_transient_openai_chatbot_blocked_', '', $result['option_name']);
            $block_data = maybe_unserialize($result['option_value']);
            
            $blocked_clients[$client_id] = $block_data;
        }
        
        return $blocked_clients;
    }

    /**
     * Clean up expired rate limit data
     */
    public function cleanup_expired_data() {
        global $wpdb;
        
        // Clean up expired transients
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_timeout_openai_chatbot_%' 
             AND option_value < UNIX_TIMESTAMP()"
        );
        
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_openai_chatbot_%' 
             AND option_name NOT IN (
                 SELECT CONCAT('_transient_', SUBSTRING(option_name, 20)) 
                 FROM {$wpdb->options} 
                 WHERE option_name LIKE '_transient_timeout_openai_chatbot_%'
             )"
        );
    }

    /**
     * Store request metadata for analytics
     * 
     * @param string $client_id Client identifier
     */
    private function store_request_metadata($client_id) {
        $metadata = array(
            'timestamp' => time(),
            'client_id' => $client_id,
            'ip' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'user_id' => is_user_logged_in() ? get_current_user_id() : null
        );
        
        // Store in transient for short-term analytics (24 hours)
        $analytics_key = 'openai_chatbot_analytics_' . time() . '_' . wp_generate_password(8, false);
        set_transient($analytics_key, $metadata, DAY_IN_SECONDS);
    }

    /**
     * Log rate limit violation
     * 
     * @param string $client_id Client identifier
     * @param string $type Violation type
     */
    private function log_rate_limit_violation($client_id, $type) {
        if (defined('OPENAI_CHATBOT_DEBUG') && OPENAI_CHATBOT_DEBUG) {
            error_log(sprintf(
                'OpenAI Chatbot Rate Limit Violation - Client: %s, Type: %s, IP: %s, Time: %s',
                $client_id,
                $type,
                $this->get_client_ip(),
                current_time('Y-m-d H:i:s')
            ));
        }
        
        // Increment violation counter
        $violation_key = 'openai_chatbot_violations_' . $client_id;
        $this->increment_counter($violation_key, HOUR_IN_SECONDS);
        
        // Auto-block after multiple violations
        $violation_count = get_transient($violation_key) ?: 0;
        if ($violation_count >= 5) {
            $this->block_client($client_id, 900, 'Multiple rate limit violations'); // 15 minutes
        }
    }

    /**
     * Log client block
     * 
     * @param string $client_id Client identifier
     * @param string $reason Block reason
     * @param int $duration Block duration
     */
    private function log_client_block($client_id, $reason, $duration) {
        error_log(sprintf(
            'OpenAI Chatbot Client Blocked - Client: %s, Reason: %s, Duration: %d seconds, IP: %s, Time: %s',
            $client_id,
            $reason,
            $duration,
            $this->get_client_ip(),
            current_time('Y-m-d H:i:s')
        ));
    }

    /**
     * Get rate limiting statistics
     * 
     * @return array Statistics
     */
    public function get_statistics() {
        global $wpdb;
        
        // Count active rate limits
        $active_limits = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_openai_chatbot_limit_%'"
        );
        
        // Count blocked clients
        $blocked_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_openai_chatbot_blocked_%'"
        );
        
        // Get global request count
        $global_requests = get_transient('openai_chatbot_global_limit') ?: 0;
        
        return array(
            'active_limits' => (int) $active_limits,
            'blocked_clients' => (int) $blocked_count,
            'global_requests_current_window' => (int) $global_requests,
            'global_limit' => $this->global_limit,
            'anonymous_limit' => $this->anonymous_limit,
            'user_limit' => $this->user_limit,
            'burst_limit' => $this->burst_limit
        );
    }
}