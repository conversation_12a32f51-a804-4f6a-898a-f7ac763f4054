<?php
/**
 * Security Handler Class
 * 
 * Implements comprehensive security measures for the OpenAI Chatbot plugin
 * Handles input sanitization, validation, nonce verification, and security logging
 * 
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * OpenAI Chatbot Security Handler
 * 
 * This class provides comprehensive security features to protect against
 * various attack vectors and ensure safe operation of the chatbot plugin.
 */
class OpenAI_Chatbot_Security_Handler {

    /**
     * Maximum message length
     * 
     * @var int
     */
    private $max_message_length = 1000;

    /**
     * Allowed HTML tags in messages
     * 
     * @var array
     */
    private $allowed_html = array();

    /**
     * Suspicious patterns to detect
     * 
     * @var array
     */
    private $suspicious_patterns = array();

    /**
     * Constructor
     * 
     * Initialize security settings and patterns
     */
    public function __construct() {
        $this->max_message_length = get_option('openai_chatbot_max_message_length', 1000);
        $this->init_security_patterns();
        $this->init_allowed_html();
    }

    /**
     * Initialize suspicious patterns for detection
     */
    private function init_security_patterns() {
        $this->suspicious_patterns = array(
            // SQL Injection patterns
            'sql_injection' => array(
                '/(\bUNION\b.*\bSELECT\b)/i',
                '/(\bSELECT\b.*\bFROM\b)/i',
                '/(\bINSERT\b.*\bINTO\b)/i',
                '/(\bUPDATE\b.*\bSET\b)/i',
                '/(\bDELETE\b.*\bFROM\b)/i',
                '/(\bDROP\b.*\bTABLE\b)/i',
                '/(\'.*OR.*\'.*=.*\')/i',
                '/(\".*OR.*\".*=.*\")/i'
            ),
            
            // XSS patterns
            'xss' => array(
                '/<script[^>]*>.*?<\/script>/is',
                '/<iframe[^>]*>.*?<\/iframe>/is',
                '/javascript:/i',
                '/on\w+\s*=/i',
                '/<object[^>]*>.*?<\/object>/is',
                '/<embed[^>]*>/i',
                '/<applet[^>]*>.*?<\/applet>/is'
            ),
            
            // Command injection patterns
            'command_injection' => array(
                '/[;&|`$(){}[\]]/i',
                '/\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp)\b/i'
            ),
            
            // Path traversal patterns
            'path_traversal' => array(
                '/\.\.\//',
                '/\.\.\\\\/',
                '/%2e%2e%2f/',
                '/%2e%2e%5c/',
                '/\.\.\%2f/',
                '/\.\.\%5c/'
            ),
            
            // LDAP injection patterns
            'ldap_injection' => array(
                '/[()&|!]/i',
                '/\*/',
                '/\\\\/i'
            ),
            
            // Email injection patterns
            'email_injection' => array(
                '/\b(to|cc|bcc|from|subject|content-type|mime-version):/i',
                '/\r\n|\r|\n/',
                '/%0a|%0d/i'
            )
        );

        // Allow filtering of patterns
        $this->suspicious_patterns = apply_filters('openai_chatbot_security_patterns', $this->suspicious_patterns);
    }

    /**
     * Initialize allowed HTML tags
     */
    private function init_allowed_html() {
        // By default, no HTML is allowed in chat messages
        $this->allowed_html = array();
        
        // Allow customization via filter
        $this->allowed_html = apply_filters('openai_chatbot_allowed_html', $this->allowed_html);
    }

    /**
     * Validate and sanitize chat message
     * 
     * @param string $message Raw message input
     * @return array Validation result with sanitized message
     */
    public function validate_message($message) {
        $result = array(
            'valid' => true,
            'message' => '',
            'errors' => array(),
            'warnings' => array(),
            'security_score' => 100
        );

        // Check if message is empty
        if (empty(trim($message))) {
            $result['valid'] = false;
            $result['errors'][] = __('Message cannot be empty.', 'openai-chatbot');
            return $result;
        }

        // Check message length
        if (strlen($message) > $this->max_message_length) {
            $result['valid'] = false;
            $result['errors'][] = sprintf(
                __('Message is too long. Maximum %d characters allowed.', 'openai-chatbot'),
                $this->max_message_length
            );
            return $result;
        }

        // Sanitize the message
        $sanitized_message = $this->sanitize_message($message);
        
        // Check for suspicious patterns
        $security_check = $this->check_suspicious_patterns($sanitized_message);
        $result['security_score'] = $security_check['score'];
        
        if (!empty($security_check['threats'])) {
            $result['warnings'] = array_merge($result['warnings'], $security_check['threats']);
            
            // Block if high-risk threats detected
            if ($security_check['score'] < 50) {
                $result['valid'] = false;
                $result['errors'][] = __('Message contains potentially harmful content.', 'openai-chatbot');
                $this->log_security_threat($message, $security_check['threats']);
                return $result;
            }
        }

        // Additional content validation
        $content_check = $this->validate_content($sanitized_message);
        if (!$content_check['valid']) {
            $result['valid'] = false;
            $result['errors'] = array_merge($result['errors'], $content_check['errors']);
        }

        $result['message'] = $sanitized_message;
        
        return $result;
    }

    /**
     * Sanitize message content
     * 
     * @param string $message Raw message
     * @return string Sanitized message
     */
    private function sanitize_message($message) {
        // Remove null bytes
        $message = str_replace(chr(0), '', $message);
        
        // Normalize line endings
        $message = str_replace(array("\r\n", "\r"), "\n", $message);
        
        // Strip HTML tags (except allowed ones)
        $message = wp_kses($message, $this->allowed_html);
        
        // Sanitize text field
        $message = sanitize_textarea_field($message);
        
        // Remove excessive whitespace
        $message = preg_replace('/\s+/', ' ', $message);
        
        // Trim whitespace
        $message = trim($message);
        
        return $message;
    }

    /**
     * Check for suspicious patterns in message
     * 
     * @param string $message Sanitized message
     * @return array Security check results
     */
    private function check_suspicious_patterns($message) {
        $result = array(
            'score' => 100,
            'threats' => array()
        );

        foreach ($this->suspicious_patterns as $threat_type => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $message)) {
                    $result['threats'][] = $threat_type;
                    
                    // Deduct points based on threat severity
                    switch ($threat_type) {
                        case 'sql_injection':
                        case 'command_injection':
                            $result['score'] -= 40;
                            break;
                        case 'xss':
                            $result['score'] -= 30;
                            break;
                        case 'path_traversal':
                        case 'ldap_injection':
                            $result['score'] -= 25;
                            break;
                        case 'email_injection':
                            $result['score'] -= 15;
                            break;
                        default:
                            $result['score'] -= 10;
                    }
                    
                    break; // Only count each threat type once
                }
            }
        }

        $result['score'] = max(0, $result['score']);
        $result['threats'] = array_unique($result['threats']);

        return $result;
    }

    /**
     * Validate message content for appropriateness
     * 
     * @param string $message Sanitized message
     * @return array Content validation result
     */
    private function validate_content($message) {
        $result = array(
            'valid' => true,
            'errors' => array()
        );

        // Check for excessive repetition (spam detection)
        if ($this->is_repetitive_content($message)) {
            $result['valid'] = false;
            $result['errors'][] = __('Message contains excessive repetition.', 'openai-chatbot');
        }

        // Check for binary or non-printable characters
        if ($this->contains_binary_content($message)) {
            $result['valid'] = false;
            $result['errors'][] = __('Message contains invalid characters.', 'openai-chatbot');
        }

        // Check message complexity (too simple might be spam)
        if (strlen($message) > 50 && $this->is_too_simple($message)) {
            // This is a warning, not an error
            // Could be used for additional filtering
        }

        return $result;
    }

    /**
     * Check if content is repetitive (spam detection)
     * 
     * @param string $message Message to check
     * @return bool True if repetitive
     */
    private function is_repetitive_content($message) {
        // Check for repeated characters
        if (preg_match('/(.)\1{10,}/', $message)) {
            return true;
        }

        // Check for repeated words
        $words = explode(' ', $message);
        if (count($words) > 5) {
            $word_counts = array_count_values($words);
            foreach ($word_counts as $count) {
                if ($count > ceil(count($words) / 3)) {
                    return true;
                }
            }
        }

        // Check for repeated phrases
        if (preg_match('/(.{3,})\1{3,}/', $message)) {
            return true;
        }

        return false;
    }

    /**
     * Check if message contains binary content
     * 
     * @param string $message Message to check
     * @return bool True if contains binary content
     */
    private function contains_binary_content($message) {
        // Check for non-printable characters (except common whitespace)
        return preg_match('/[^\x20-\x7E\x09\x0A\x0D]/', $message);
    }

    /**
     * Check if message is too simple (potential spam)
     * 
     * @param string $message Message to check
     * @return bool True if too simple
     */
    private function is_too_simple($message) {
        // Calculate entropy (randomness)
        $entropy = $this->calculate_entropy($message);
        
        // Low entropy might indicate spam or bot-generated content
        return $entropy < 2.0;
    }

    /**
     * Calculate Shannon entropy of a string
     * 
     * @param string $string Input string
     * @return float Entropy value
     */
    private function calculate_entropy($string) {
        $length = strlen($string);
        if ($length <= 1) {
            return 0;
        }

        $frequencies = array_count_values(str_split($string));
        $entropy = 0;

        foreach ($frequencies as $frequency) {
            $probability = $frequency / $length;
            $entropy -= $probability * log($probability, 2);
        }

        return $entropy;
    }

    /**
     * Verify WordPress nonce
     * 
     * @param string $nonce Nonce value
     * @param string $action Nonce action
     * @return bool True if valid
     */
    public function verify_nonce($nonce, $action = 'openai_chatbot_nonce') {
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Check user capabilities
     * 
     * @param string $capability Required capability
     * @return bool True if user has capability
     */
    public function check_user_capability($capability = 'read') {
        return current_user_can($capability);
    }

    /**
     * Validate request origin
     * 
     * @return bool True if request is from valid origin
     */
    public function validate_request_origin() {
        // Check if request is from the same site
        $referer = wp_get_referer();
        if (!$referer) {
            return false;
        }

        $site_url = home_url();
        return strpos($referer, $site_url) === 0;
    }

    /**
     * Log security threat
     * 
     * @param string $message Original message
     * @param array $threats Detected threats
     */
    private function log_security_threat($message, $threats) {
        $log_data = array(
            'timestamp' => current_time('Y-m-d H:i:s'),
            'ip' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'user_id' => is_user_logged_in() ? get_current_user_id() : null,
            'message_preview' => substr($message, 0, 100) . '...',
            'threats' => $threats,
            'referer' => wp_get_referer()
        );

        // Log to WordPress error log
        error_log('OpenAI Chatbot Security Threat: ' . json_encode($log_data));

        // Store in transient for admin review (24 hours)
        $threat_key = 'openai_chatbot_threat_' . time() . '_' . wp_generate_password(8, false);
        set_transient($threat_key, $log_data, DAY_IN_SECONDS);

        // Trigger action for custom handling
        do_action('openai_chatbot_security_threat', $log_data);
    }

    /**
     * Get client IP address
     * 
     * @return string IP address
     */
    private function get_client_ip() {
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );

        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Get security threats from last 24 hours
     * 
     * @return array List of security threats
     */
    public function get_recent_threats() {
        global $wpdb;
        
        $threats = array();
        
        $results = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_openai_chatbot_threat_%'",
            ARRAY_A
        );
        
        foreach ($results as $result) {
            $threat_data = maybe_unserialize($result['option_value']);
            if ($threat_data) {
                $threats[] = $threat_data;
            }
        }
        
        // Sort by timestamp (newest first)
        usort($threats, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        return $threats;
    }

    /**
     * Clean up old security logs
     */
    public function cleanup_security_logs() {
        global $wpdb;
        
        // Remove expired threat logs
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_timeout_openai_chatbot_threat_%' 
             AND option_value < UNIX_TIMESTAMP()"
        );
        
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_openai_chatbot_threat_%' 
             AND option_name NOT IN (
                 SELECT CONCAT('_transient_', SUBSTRING(option_name, 20)) 
                 FROM {$wpdb->options} 
                 WHERE option_name LIKE '_transient_timeout_openai_chatbot_threat_%'
             )"
        );
    }

    /**
     * Generate secure token
     * 
     * @param int $length Token length
     * @return string Secure token
     */
    public function generate_secure_token($length = 32) {
        return wp_generate_password($length, false, false);
    }

    /**
     * Hash sensitive data
     * 
     * @param string $data Data to hash
     * @return string Hashed data
     */
    public function hash_data($data) {
        return wp_hash($data);
    }

    /**
     * Encrypt sensitive data
     * 
     * @param string $data Data to encrypt
     * @param string $key Encryption key
     * @return string Encrypted data
     */
    public function encrypt_data($data, $key = null) {
        if (!$key) {
            $key = wp_salt('auth');
        }
        
        // Simple XOR encryption (for basic obfuscation)
        $encrypted = '';
        $key_length = strlen($key);
        
        for ($i = 0; $i < strlen($data); $i++) {
            $encrypted .= chr(ord($data[$i]) ^ ord($key[$i % $key_length]));
        }
        
        return base64_encode($encrypted);
    }

    /**
     * Decrypt sensitive data
     * 
     * @param string $encrypted_data Encrypted data
     * @param string $key Encryption key
     * @return string Decrypted data
     */
    public function decrypt_data($encrypted_data, $key = null) {
        if (!$key) {
            $key = wp_salt('auth');
        }
        
        $data = base64_decode($encrypted_data);
        $decrypted = '';
        $key_length = strlen($key);
        
        for ($i = 0; $i < strlen($data); $i++) {
            $decrypted .= chr(ord($data[$i]) ^ ord($key[$i % $key_length]));
        }
        
        return $decrypted;
    }
}