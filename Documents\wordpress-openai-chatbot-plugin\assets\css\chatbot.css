/**
 * OpenAI Chatbot Styles
 * 
 * Comprehensive CSS for the chatbot interface with responsive design,
 * animations, and accessibility features
 * 
 * @package OpenAI_Chatbot
 */

/* CSS Variables for theming */
:root {
    --chatbot-primary-color: #007cba;
    --chatbot-primary-hover: #005a87;
    --chatbot-secondary-color: #f0f0f1;
    --chatbot-text-color: #1e1e1e;
    --chatbot-text-light: #757575;
    --chatbot-border-color: #ddd;
    --chatbot-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    --chatbot-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --chatbot-border-radius: 12px;
    --chatbot-border-radius-small: 8px;
    --chatbot-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --chatbot-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 'Helvetica Neue', sans-serif;
    --chatbot-z-index: 999999;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --chatbot-primary-color: #4f94cd;
        --chatbot-primary-hover: #6ba3d6;
        --chatbot-secondary-color: #2c2c2c;
        --chatbot-text-color: #ffffff;
        --chatbot-text-light: #b0b0b0;
        --chatbot-border-color: #444;
        --chatbot-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        --chatbot-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
}

/* Reset and base styles */
.openai-chatbot-widget,
.openai-chatbot-widget * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.openai-chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: var(--chatbot-z-index);
    font-family: var(--chatbot-font-family);
    font-size: 14px;
    line-height: 1.4;
    color: var(--chatbot-text-color);
    direction: ltr;
    text-align: left;
}

/* Widget container responsive positioning */
@media (max-width: 768px) {
    .openai-chatbot-widget {
        bottom: 10px;
        right: 10px;
        left: 10px;
        width: auto;
    }
}

/* Toggle button */
.chatbot-toggle {
    position: relative;
    width: 60px;
    height: 60px;
    background: var(--chatbot-primary-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--chatbot-shadow);
    transition: var(--chatbot-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
}

.chatbot-toggle:hover {
    background: var(--chatbot-primary-hover);
    transform: scale(1.05);
}

.chatbot-toggle:active {
    transform: scale(0.95);
}

/* Toggle icons */
.toggle-icon {
    position: relative;
    width: 24px;
    height: 24px;
    color: white;
}

.toggle-icon svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: var(--chatbot-transition);
}

.openai-chatbot-widget[data-status="closed"] .toggle-icon .close-icon {
    opacity: 0;
    transform: rotate(90deg);
}

.openai-chatbot-widget[data-status="open"] .toggle-icon .chat-icon {
    opacity: 0;
    transform: rotate(-90deg);
}

/* Connection indicator */
.connection-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
}

.status-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #4caf50;
    transition: var(--chatbot-transition);
}

.status-dot.disconnected {
    background: #f44336;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chat window */
.chatbot-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 500px;
    background: white;
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    transition: var(--chatbot-transition);
    pointer-events: none;
}

.openai-chatbot-widget[data-status="open"] .chatbot-window {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

/* Mobile responsive chat window */
@media (max-width: 768px) {
    .chatbot-window {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
        transform: translateY(100%);
    }
    
    .openai-chatbot-widget[data-status="open"] .chatbot-window {
        transform: translateY(0);
    }
}

/* Chat header */
.chatbot-header {
    background: var(--chatbot-primary-color);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bot-avatar {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bot-avatar svg {
    width: 20px;
    height: 20px;
}

.bot-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.bot-info p {
    font-size: 12px;
    opacity: 0.8;
    margin: 0;
}

.minimize-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--chatbot-border-radius-small);
    transition: var(--chatbot-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.minimize-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.minimize-btn svg {
    width: 20px;
    height: 20px;
}

/* Messages area */
.chatbot-messages {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--chatbot-secondary-color);
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    scroll-behavior: smooth;
}

/* Custom scrollbar */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Message styles */
.message {
    display: flex;
    margin-bottom: 16px;
    opacity: 0;
    transform: translateY(10px);
    transition: var(--chatbot-transition);
    align-items: flex-start;
    gap: 8px;
}

.message.message-visible {
    opacity: 1;
    transform: translateY(0);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: var(--chatbot-primary-color);
    color: white;
}

.message-avatar svg {
    width: 18px;
    height: 18px;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: var(--chatbot-text-light);
}

.message-bubble {
    max-width: 70%;
    position: relative;
}

.message-content {
    background: white;
    padding: 12px 16px;
    border-radius: var(--chatbot-border-radius);
    box-shadow: var(--chatbot-shadow-light);
    word-wrap: break-word;
    line-height: 1.5;
}

.user-message .message-content {
    background: var(--chatbot-primary-color);
    color: white;
}

.bot-message .message-content {
    background: white;
    border: 1px solid var(--chatbot-border-color);
}

.system-message .message-content {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    font-style: italic;
    text-align: center;
}

.error-message .message-content {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message-time {
    font-size: 11px;
    color: var(--chatbot-text-light);
    margin-top: 4px;
    text-align: right;
}

.user-message .message-time {
    text-align: left;
}

/* Message content formatting */
.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content a {
    color: var(--chatbot-primary-color);
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.user-message .message-content a {
    color: rgba(255, 255, 255, 0.9);
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.8);
    border-top: 1px solid var(--chatbot-border-color);
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--chatbot-primary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.typing-text {
    font-size: 12px;
    color: var(--chatbot-text-light);
    font-style: italic;
}

/* Input area */
.chatbot-input {
    background: white;
    border-top: 1px solid var(--chatbot-border-color);
    flex-shrink: 0;
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    padding: 16px;
}

.message-input {
    flex: 1;
    border: 1px solid var(--chatbot-border-color);
    border-radius: var(--chatbot-border-radius-small);
    padding: 12px 16px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    transition: var(--chatbot-transition);
    min-height: 44px;
    max-height: 120px;
}

.message-input:focus {
    border-color: var(--chatbot-primary-color);
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.message-input::placeholder {
    color: var(--chatbot-text-light);
}

.send-button {
    width: 44px;
    height: 44px;
    background: var(--chatbot-primary-color);
    border: none;
    border-radius: var(--chatbot-border-radius-small);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--chatbot-transition);
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: var(--chatbot-primary-hover);
    transform: scale(1.05);
}

.send-button:active:not(:disabled) {
    transform: scale(0.95);
}

.send-button:disabled {
    background: var(--chatbot-text-light);
    cursor: not-allowed;
    transform: none;
}

.send-button svg {
    width: 20px;
    height: 20px;
    transition: var(--chatbot-transition);
}

.loading-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Input footer */
.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px 12px;
    font-size: 11px;
    color: var(--chatbot-text-light);
}

.character-count.warning {
    color: #f44336;
    font-weight: 600;
}

.powered-by {
    opacity: 0.7;
}

/* Welcome message */
.welcome-message {
    margin-bottom: 16px;
}

.welcome-message .message {
    opacity: 1;
    transform: none;
}

/* Accessibility improvements */
.chatbot-toggle:focus,
.minimize-btn:focus,
.send-button:focus,
.message-input:focus {
    outline: 2px solid var(--chatbot-primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --chatbot-border-color: #000;
        --chatbot-text-light: #000;
        --chatbot-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    }
    
    .message-content {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .messages-container {
        scroll-behavior: auto;
    }
}

/* Print styles */
@media print {
    .openai-chatbot-widget {
        display: none;
    }
}

/* RTL support */
[dir="rtl"] .openai-chatbot-widget {
    left: 20px;
    right: auto;
}

[dir="rtl"] .chatbot-window {
    left: 0;
    right: auto;
}

[dir="rtl"] .user-message {
    flex-direction: row;
}

[dir="rtl"] .message-time {
    text-align: left;
}

[dir="rtl"] .user-message .message-time {
    text-align: right;
}

/* Custom theme classes */
.openai-chatbot-widget.theme-dark {
    --chatbot-primary-color: #4f94cd;
    --chatbot-primary-hover: #6ba3d6;
    --chatbot-secondary-color: #2c2c2c;
    --chatbot-text-color: #ffffff;
    --chatbot-text-light: #b0b0b0;
    --chatbot-border-color: #444;
}

.openai-chatbot-widget.theme-minimal {
    --chatbot-border-radius: 4px;
    --chatbot-border-radius-small: 4px;
    --chatbot-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --chatbot-shadow-light: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Animation classes for JavaScript */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.no-scroll {
    overflow: hidden;
}

/* Mobile-specific adjustments */
@media (max-width: 480px) {
    .chatbot-toggle {
        width: 56px;
        height: 56px;
    }
    
    .toggle-icon {
        width: 22px;
        height: 22px;
    }
    
    .message-bubble {
        max-width: 85%;
    }
    
    .input-container {
        padding: 12px;
    }
    
    .message-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .chatbot-window {
        width: 360px;
        height: 480px;
    }
}

/* Large screen adjustments */
@media (min-width: 1200px) {
    .chatbot-window {
        width: 400px;
        height: 520px;
    }
}