/**
 * OpenAI Chatbot Admin Styles
 * 
 * CSS styles for the WordPress admin interface
 * Provides clean, professional styling for the settings page
 * 
 * @package OpenAI_Chatbot
 */

/* Admin page layout */
.openai-chatbot-admin {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.admin-content {
    flex: 2;
}

.admin-sidebar {
    flex: 1;
    max-width: 300px;
}

/* Postbox styling */
.openai-chatbot-admin .postbox {
    margin-bottom: 20px;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.openai-chatbot-admin .postbox-header {
    border-bottom: 1px solid #c3c4c7;
    background: #f6f7f7;
}

.openai-chatbot-admin .postbox-header h2 {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
}

.openai-chatbot-admin .inside {
    padding: 12px;
}

/* Form styling */
.openai-chatbot-admin .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.openai-chatbot-admin .form-table td {
    padding: 15px 10px;
}

.openai-chatbot-admin .form-table input[type="text"],
.openai-chatbot-admin .form-table input[type="number"],
.openai-chatbot-admin .form-table select,
.openai-chatbot-admin .form-table textarea {
    width: 100%;
    max-width: 400px;
}

.openai-chatbot-admin .form-table textarea.large-text {
    max-width: 100%;
}

.openai-chatbot-admin .form-table .small-text {
    width: 80px;
    max-width: 80px;
}

.openai-chatbot-admin .form-table .regular-text {
    width: 300px;
    max-width: 300px;
}

/* Checkbox and radio styling */
.openai-chatbot-admin .form-table fieldset {
    margin: 0;
    padding: 0;
    border: none;
}

.openai-chatbot-admin .form-table fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.openai-chatbot-admin .form-table input[type="checkbox"],
.openai-chatbot-admin .form-table input[type="radio"] {
    margin-right: 8px;
    width: auto;
}

/* Description text */
.openai-chatbot-admin .description {
    font-style: italic;
    color: #646970;
    margin-top: 5px;
    font-size: 13px;
    line-height: 1.4;
}

/* Code blocks */
.openai-chatbot-admin code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-family: Consolas, Monaco, monospace;
}

.openai-chatbot-admin .code {
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
}

/* Sidebar specific styling */
.admin-sidebar .postbox {
    margin-bottom: 20px;
}

.admin-sidebar .inside {
    padding: 12px;
}

.admin-sidebar .inside p {
    margin: 0 0 10px 0;
    line-height: 1.5;
}

.admin-sidebar .inside ul {
    margin: 10px 0;
    padding-left: 20px;
}

.admin-sidebar .inside li {
    margin-bottom: 5px;
    line-height: 1.4;
}

.admin-sidebar .inside h4 {
    margin: 15px 0 8px 0;
    font-size: 13px;
    font-weight: 600;
}

.admin-sidebar .inside h4:first-child {
    margin-top: 0;
}

/* Statistics table */
.admin-sidebar table.widefat {
    border: 1px solid #c3c4c7;
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

.admin-sidebar table.widefat td {
    padding: 8px 10px;
    border-bottom: 1px solid #c3c4c7;
    font-size: 13px;
}

.admin-sidebar table.widefat tr:last-child td {
    border-bottom: none;
}

.admin-sidebar table.widefat td:first-child {
    font-weight: 500;
}

.admin-sidebar table.widefat td:last-child {
    text-align: right;
}

/* Button styling */
.openai-chatbot-admin .button {
    margin-right: 10px;
}

.openai-chatbot-admin .button-primary {
    background: #2271b1;
    border-color: #2271b1;
}

.openai-chatbot-admin .button-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

.openai-chatbot-admin .button-secondary {
    background: #f6f7f7;
    border-color: #c3c4c7;
    color: #2c3338;
}

.openai-chatbot-admin .button-secondary:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
}

/* Notice styling */
.openai-chatbot-admin .notice {
    margin: 15px 0;
    padding: 12px;
    border-left: 4px solid #72aee6;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.openai-chatbot-admin .notice-success {
    border-left-color: #00a32a;
}

.openai-chatbot-admin .notice-warning {
    border-left-color: #dba617;
}

.openai-chatbot-admin .notice-error {
    border-left-color: #d63638;
}

.openai-chatbot-admin .notice p {
    margin: 0.5em 0;
    padding: 2px;
}

.openai-chatbot-admin .notice code {
    display: block;
    margin: 8px 0;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.4;
}

/* API configuration warnings */
.openai-chatbot-admin .notice-warning strong {
    color: #b32d2e;
}

/* Responsive design */
@media (max-width: 1200px) {
    .openai-chatbot-admin {
        flex-direction: column;
    }
    
    .admin-sidebar {
        max-width: none;
    }
    
    .openai-chatbot-admin .form-table th {
        width: 150px;
    }
}

@media (max-width: 782px) {
    .openai-chatbot-admin .form-table th,
    .openai-chatbot-admin .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .openai-chatbot-admin .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .openai-chatbot-admin .form-table td {
        padding-top: 0;
        padding-bottom: 15px;
        border-bottom: 1px solid #e1e1e1;
    }
    
    .openai-chatbot-admin .form-table input[type="text"],
    .openai-chatbot-admin .form-table input[type="number"],
    .openai-chatbot-admin .form-table select,
    .openai-chatbot-admin .form-table textarea {
        max-width: 100%;
    }
}

/* Loading states */
.openai-chatbot-admin .loading {
    opacity: 0.6;
    pointer-events: none;
}

.openai-chatbot-admin .spinner {
    float: none;
    margin: 0 5px;
}

/* Accessibility improvements */
.openai-chatbot-admin .screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.openai-chatbot-admin input:focus,
.openai-chatbot-admin select:focus,
.openai-chatbot-admin textarea:focus,
.openai-chatbot-admin button:focus {
    outline: 2px solid #2271b1;
    outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .openai-chatbot-admin .postbox {
        border-color: #000;
    }
    
    .openai-chatbot-admin .postbox-header {
        background: #fff;
        border-bottom-color: #000;
    }
    
    .openai-chatbot-admin .form-table td {
        border-bottom-color: #000;
    }
}

/* Print styles */
@media print {
    .openai-chatbot-admin .admin-sidebar {
        display: none;
    }
    
    .openai-chatbot-admin .button {
        display: none;
    }
    
    .openai-chatbot-admin .postbox {
        border: 1px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
}

/* Custom utility classes */
.openai-chatbot-admin .text-center {
    text-align: center;
}

.openai-chatbot-admin .text-right {
    text-align: right;
}

.openai-chatbot-admin .margin-top {
    margin-top: 20px;
}

.openai-chatbot-admin .margin-bottom {
    margin-bottom: 20px;
}

.openai-chatbot-admin .padding {
    padding: 15px;
}

.openai-chatbot-admin .no-margin {
    margin: 0;
}

.openai-chatbot-admin .no-padding {
    padding: 0;
}

/* Status indicators */
.openai-chatbot-admin .status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
}

.openai-chatbot-admin .status-indicator.connected {
    background: #00a32a;
}

.openai-chatbot-admin .status-indicator.disconnected {
    background: #d63638;
}

.openai-chatbot-admin .status-indicator.warning {
    background: #dba617;
}

/* Tabs (if needed for future expansion) */
.openai-chatbot-admin .nav-tab-wrapper {
    border-bottom: 1px solid #c3c4c7;
    margin: 0 0 20px 0;
    padding: 0;
}

.openai-chatbot-admin .nav-tab {
    background: #f1f1f1;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    color: #2c3338;
    text-decoration: none;
    padding: 8px 12px;
    margin: 0 5px -1px 0;
    display: inline-block;
    font-size: 13px;
    line-height: 1.4;
}

.openai-chatbot-admin .nav-tab:hover {
    background: #fff;
    color: #2c3338;
}

.openai-chatbot-admin .nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #000;
}

/* Animation for smooth transitions */
.openai-chatbot-admin .postbox,
.openai-chatbot-admin .notice {
    transition: all 0.3s ease;
}

.openai-chatbot-admin .postbox:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Dark mode support (WordPress 5.7+) */
@media (prefers-color-scheme: dark) {
    .openai-chatbot-admin .postbox {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    .openai-chatbot-admin .postbox-header {
        background: #23282d;
        border-bottom-color: #3c434a;
        color: #f0f0f1;
    }
    
    .openai-chatbot-admin .inside {
        color: #f0f0f1;
    }
    
    .openai-chatbot-admin .description {
        color: #a7aaad;
    }
    
    .openai-chatbot-admin code {
        background: #2c3338;
        color: #f0f0f1;
    }
}